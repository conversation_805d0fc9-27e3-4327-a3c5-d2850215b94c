# 导出API参数修正说明

## 🚨 重要修正

经过对实际代码的详细检查，发现所有导出接口都需要额外的 `export_type` 参数。

## 问题原因

1. 所有专用导出DTO都继承自 `BaseExportDTO`
2. `BaseExportDTO` 中 `export_type` 字段被标记为 `required()`
3. 虽然控制器试图在接收请求后手动设置这个值，但验证在控制器方法执行前就进行了
4. 因此前端必须在请求中提供这个参数

## 修正后的参数要求

### 所有接口都需要的必填参数
- `export_type`: 导出类型（必填）
- `sso_school_code`: 学校编码（必填）

### 各接口的 export_type 值

| 接口 | export_type 值 |
|------|----------------|
| `/api/export/school-statistics` | `"school_statistics"` |
| `/api/export/teacher-statistics` | `"teacher_statistics"` |
| `/api/export/teacher-ranking` | `"teacher_ranking"` |
| `/api/export/questionnaire-responses` | `"questionnaire_responses"` |
| `/api/export/comprehensive-report` | `"comprehensive_report"` |
| `/api/export/incomplete-students` | `"incomplete_students"` |

## 修正后的调用示例

### 学校统计导出
```javascript
const response = await fetch('/api/export/school-statistics', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    export_type: 'school_statistics',  // 必填
    sso_school_code: 'SCHOOL001',      // 必填
    questionnaire_id: 123,             // 可选，使用缓存数据
    month: '2024-03',                  // 可选
    include_trend: true,               // 可选
    include_teacher_ranking: true      // 可选
  })
});
```

### 教师排名导出
```javascript
const response = await fetch('/api/export/teacher-ranking', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    export_type: 'teacher_ranking',    // 必填
    sso_school_code: 'SCHOOL001',      // 必填
    month: '2024-03',                  // 可选
    sort_by: 'average_score',          // 可选
    sort_order: 'DESC',                // 可选
    limit: 50                          // 可选
  })
});
```

### 教师统计导出
```javascript
const response = await fetch('/api/export/teacher-statistics', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    export_type: 'teacher_statistics', // 必填
    sso_school_code: 'SCHOOL001',      // 必填
    sso_teacher_id: 'TEACHER001',      // 必填
    month: '2024-03',                  // 可选
    include_distribution: true,        // 可选
    include_keywords: true,            // 可选
    include_trend: true                // 可选
  })
});
```

### 问卷响应导出
```javascript
const response = await fetch('/api/export/questionnaire-responses', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    export_type: 'questionnaire_responses', // 必填
    sso_school_code: 'SCHOOL001',           // 必填
    questionnaire_id: 1,                    // 可选
    month: '2024-03',                       // 可选
    grade_code: '7',                        // 可选
    class_code: '1',                        // 可选
    include_answers: true                   // 可选
  })
});
```

### 综合报表导出
```javascript
const response = await fetch('/api/export/comprehensive-report', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    export_type: 'comprehensive_report',    // 必填
    sso_school_code: 'SCHOOL001',           // 必填
    month: '2024-03',                       // 可选
    include_school_summary: true,           // 可选
    include_teacher_ranking: true,          // 可选
    include_trend_analysis: true,           // 可选
    include_completion_analysis: true       // 可选
  })
});
```

### 未完成学生导出
```javascript
const response = await fetch('/api/export/incomplete-students', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    export_type: 'incomplete_students',     // 必填
    sso_school_code: 'SCHOOL001',           // 必填
    questionnaire_id: 1,                    // 可选
    month: '2024-03',                       // 可选
    grade_code: '7',                        // 可选
    include_contact_info: true              // 可选
  })
});
```

## 通用导出接口

通用导出接口 `/api/export/general` 的设计是正确的，它本来就需要 `export_type` 参数来区分导出类型。

```javascript
const response = await fetch('/api/export/general', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    export_type: 'school_statistics',      // 必填，指定导出类型
    sso_school_code: 'SCHOOL001',          // 必填
    // ... 其他参数根据导出类型而定
  })
});
```

## 建议的代码修复

为了避免这种混淆，建议修改后端代码：

### 方案1：修改DTO继承关系
为每个专用接口创建独立的DTO，不继承BaseExportDTO，避免不必要的export_type字段。

### 方案2：修改验证逻辑
在BaseExportDTO中将export_type字段改为可选，在控制器中手动设置。

### 方案3：保持现状
明确文档说明所有接口都需要export_type参数，这样设计也有其合理性。

## 总结

所有导出接口的正确调用都需要包含：
1. `export_type`: 对应的导出类型字符串（必填）
2. `sso_school_code`: 学校编码（必填）
3. 其他参数根据具体接口而定

请确保前端调用时包含正确的 `export_type` 参数值。
