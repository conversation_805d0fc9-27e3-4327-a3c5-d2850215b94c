import { createApp, close } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import { ExportService } from '../../src/service/export.service';
import { StatisticsService } from '../../src/service/statistics.service';
import { ExportType, ExportFormat } from '../../src/dto/export.dto';

describe('test/service/export-cached.service.test.ts', () => {
  let app;
  let exportService: ExportService;
  let statisticsService: StatisticsService;

  beforeAll(async () => {
    try {
      app = await createApp<Framework>();
      exportService = await app.getApplicationContext().getAsync(ExportService);
      statisticsService = await app.getApplicationContext().getAsync(StatisticsService);
    } catch (err) {
      console.error('setup error', err);
      throw err;
    }
  });

  afterAll(async () => {
    await close(app);
  });

  it('should use cached statistics when available', async () => {
    // 模拟缓存统计数据
    const mockCachedStats = {
      id: 1,
      questionnaire_id: 123,
      sso_school_code: 'TEST_SCHOOL',
      month: '2024-03',
      status: 'completed',
      school_statistics: {
        sso_school_name: '测试学校',
        total_responses: 100,
        completed_responses: 95,
        total_students: 120,
        completion_rate: 79.17,
        school_average_score: 85.5,
        teacher_average_score: 87.2,
      },
      grade_statistics: [
        {
          grade_name: '七年级',
          total_students: 60,
          completed_responses: 55,
          completion_rate: 91.67,
        },
        {
          grade_name: '八年级',
          total_students: 60,
          completed_responses: 40,
          completion_rate: 66.67,
        },
      ],
      teacher_statistics: [
        {
          sso_teacher_id: 'TEACHER001',
          sso_teacher_name: '张老师',
          sso_teacher_subject: '数学',
          sso_teacher_department: '初中部',
          average_score: 90.5,
          evaluation_count: 25,
          recommendation_rate: 92.0,
        },
        {
          sso_teacher_id: 'TEACHER002',
          sso_teacher_name: '李老师',
          sso_teacher_subject: '语文',
          sso_teacher_department: '初中部',
          average_score: 88.3,
          evaluation_count: 30,
          recommendation_rate: 86.7,
        },
      ],
      calculation_end_time: new Date(),
    };

    // 模拟 getCachedStatistics 方法
    jest.spyOn(statisticsService, 'getCachedStatistics').mockResolvedValue(mockCachedStats);

    const exportDto = {
      export_type: ExportType.SCHOOL_STATISTICS,
      sso_school_code: 'TEST_SCHOOL',
      questionnaire_id: 123,
      month: '2024-03',
      export_format: ExportFormat.XLSX,
      include_trend: true,
      include_teacher_ranking: true,
      include_incomplete_students: true,
    };

    const result = await exportService.exportToExcel(exportDto);

    expect(result).toBeDefined();
    expect(result.buffer).toBeInstanceOf(Buffer);
    expect(result.filename).toContain('学校统计报表');
    expect(result.filename).toContain('.xlsx');
    expect(result.contentType).toBe('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

    // 验证是否调用了缓存方法
    expect(statisticsService.getCachedStatistics).toHaveBeenCalledWith(123);
  });

  it('should fallback to real-time calculation when cached data is not available', async () => {
    // 模拟缓存数据不可用
    jest.spyOn(statisticsService, 'getCachedStatistics').mockResolvedValue(null);
    
    // 模拟实时统计方法
    const mockRealTimeStats = {
      sso_school_code: 'TEST_SCHOOL',
      sso_school_name: '测试学校',
      month: '2024-03',
      total_responses: 100,
      completed_responses: 95,
      total_students: 120,
      completion_rate: 79.17,
      school_average_score: 85.5,
      teacher_average_score: 87.2,
      total_teachers_evaluated: 15,
    };

    jest.spyOn(statisticsService, 'getSchoolStatistics').mockResolvedValue(mockRealTimeStats);

    const exportDto = {
      export_type: ExportType.SCHOOL_STATISTICS,
      sso_school_code: 'TEST_SCHOOL',
      questionnaire_id: 123,
      month: '2024-03',
      export_format: ExportFormat.XLSX,
      include_trend: false,
      include_teacher_ranking: false,
      include_incomplete_students: false,
    };

    const result = await exportService.exportToExcel(exportDto);

    expect(result).toBeDefined();
    expect(result.buffer).toBeInstanceOf(Buffer);
    expect(result.filename).toContain('学校统计报表');

    // 验证是否尝试了缓存方法，然后回退到实时计算
    expect(statisticsService.getCachedStatistics).toHaveBeenCalledWith(123);
    expect(statisticsService.getSchoolStatistics).toHaveBeenCalled();
  });

  it('should fallback when cached data status is not completed', async () => {
    // 模拟缓存数据状态为计算中
    const mockIncompleteStats = {
      id: 1,
      questionnaire_id: 123,
      sso_school_code: 'TEST_SCHOOL',
      month: '2024-03',
      status: 'calculating', // 状态为计算中
      school_statistics: null,
      grade_statistics: [],
      teacher_statistics: [],
    };

    jest.spyOn(statisticsService, 'getCachedStatistics').mockResolvedValue(mockIncompleteStats);
    
    // 模拟实时统计方法
    const mockRealTimeStats = {
      sso_school_code: 'TEST_SCHOOL',
      sso_school_name: '测试学校',
      month: '2024-03',
      total_responses: 50,
      completed_responses: 45,
      total_students: 60,
      completion_rate: 75.0,
      school_average_score: 82.3,
      teacher_average_score: 84.1,
      total_teachers_evaluated: 8,
    };

    jest.spyOn(statisticsService, 'getSchoolStatistics').mockResolvedValue(mockRealTimeStats);

    const exportDto = {
      export_type: ExportType.SCHOOL_STATISTICS,
      sso_school_code: 'TEST_SCHOOL',
      questionnaire_id: 123,
      month: '2024-03',
      export_format: ExportFormat.XLSX,
    };

    const result = await exportService.exportToExcel(exportDto);

    expect(result).toBeDefined();
    expect(result.buffer).toBeInstanceOf(Buffer);

    // 验证是否尝试了缓存方法，但由于状态不是completed，回退到实时计算
    expect(statisticsService.getCachedStatistics).toHaveBeenCalledWith(123);
    expect(statisticsService.getSchoolStatistics).toHaveBeenCalled();
  });

  it('should handle cached data transformation correctly', async () => {
    const mockCachedStats = {
      id: 1,
      questionnaire_id: 456,
      sso_school_code: 'SCHOOL002',
      month: '2024-04',
      status: 'completed',
      school_statistics: {
        sso_school_name: '另一个测试学校',
        total_responses: 200,
        completed_responses: 180,
        total_students: 220,
        completion_rate: 81.82,
        school_average_score: 88.7,
        teacher_average_score: 89.1,
      },
      grade_statistics: [
        {
          grade_name: '九年级',
          total_students: 110,
          completed_responses: 95,
          completion_rate: 86.36,
        },
        {
          grade_name: '八年级',
          total_students: 110,
          completed_responses: 85,
          completion_rate: 77.27,
        },
      ],
      teacher_statistics: [
        {
          sso_teacher_id: 'TEACHER003',
          sso_teacher_name: '王老师',
          sso_teacher_subject: '英语',
          sso_teacher_department: '初中部',
          average_score: 92.1,
          evaluation_count: 35,
          recommendation_rate: 94.3,
        },
      ],
      calculation_end_time: new Date(),
    };

    jest.spyOn(statisticsService, 'getCachedStatistics').mockResolvedValue(mockCachedStats);

    const exportDto = {
      export_type: ExportType.SCHOOL_STATISTICS,
      sso_school_code: 'SCHOOL002',
      questionnaire_id: 456,
      month: '2024-04',
      export_format: ExportFormat.XLSX,
      include_trend: true,
      include_teacher_ranking: true,
      include_incomplete_students: true,
    };

    const result = await exportService.exportToExcel(exportDto);

    expect(result).toBeDefined();
    expect(result.buffer).toBeInstanceOf(Buffer);
    expect(result.filename).toContain('SCHOOL002');
    expect(result.filename).toContain('2024-04');

    // 验证缓存方法被正确调用
    expect(statisticsService.getCachedStatistics).toHaveBeenCalledWith(456);
  });

  afterEach(() => {
    // 清理所有的mock
    jest.restoreAllMocks();
  });
});
