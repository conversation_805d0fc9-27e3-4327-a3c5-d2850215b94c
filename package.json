{"name": "my-midway-project", "version": "1.0.0", "description": "", "private": true, "dependencies": {"@midwayjs/axios": "^3.20.4", "@midwayjs/bootstrap": "^3.12.0", "@midwayjs/core": "^3.12.0", "@midwayjs/info": "^3.12.0", "@midwayjs/jwt": "^3.20.5", "@midwayjs/koa": "^3.12.0", "@midwayjs/logger": "^3.1.0", "@midwayjs/sequelize": "^3.20.5", "@midwayjs/validate": "^3.12.0", "exceljs": "^4.4.0", "mysql2": "^3.14.1", "sequelize": "^6.37.7", "sequelize-typescript": "^2.1.6"}, "devDependencies": {"@midwayjs/mock": "^3.12.0", "@types/jest": "^29.2.0", "@types/node": "14", "cross-env": "^6.0.0", "jest": "^29.2.2", "mwts": "^1.3.0", "mwtsc": "^1.4.0", "ts-jest": "^29.0.3", "typescript": "~4.8.0"}, "engines": {"node": ">=12.0.0"}, "scripts": {"start": "NODE_ENV=production node ./bootstrap.js", "dev": "cross-env NODE_ENV=local mwtsc --watch --run @midwayjs/mock/app.js", "test": "cross-env NODE_ENV=unittest jest", "cov": "jest --coverage", "lint": "mwts check", "lint:fix": "mwts fix", "ci": "npm run cov", "build": "mwtsc --cleanOutDir"}, "repository": {"type": "git", "url": ""}, "author": "anonymous", "license": "MIT"}