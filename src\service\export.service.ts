import { Provide, Inject } from '@midwayjs/core';
import { InjectRepository, InjectDataSource } from '@midwayjs/sequelize';
import { Repository } from 'sequelize-typescript';
import { Sequelize } from 'sequelize';
import { Context } from '@midwayjs/koa';
import * as ExcelJS from 'exceljs';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { CustomError } from '../error/custom.error';
import { Response } from '../entity/response.entity';
import { Answer } from '../entity/answer.entity';
import { Questionnaire } from '../entity/questionnaire.entity';
import { StatisticsService } from './statistics.service';
import { ResponseService } from './response.service';
import {
  BaseExportDTO,
  SchoolStatisticsExportDTO,
  TeacherStatisticsExportDTO,
  TeacherRankingExportDTO,
  QuestionnaireResponsesExportDTO,
  ComprehensiveReportExportDTO,
  IncompleteStudentsExportDTO,
  ExportType,
} from '../dto/export.dto';

@Provide()
export class ExportService {
  @InjectRepository(Response)
  responseRepository: Repository<Response>;

  @InjectRepository(Answer)
  answerRepository: Repository<Answer>;

  @InjectRepository(Questionnaire)
  questionnaireRepository: Repository<Questionnaire>;

  @InjectDataSource()
  sequelize: Sequelize;

  @Inject()
  ctx: Context;

  @Inject()
  statisticsService: StatisticsService;

  @Inject()
  responseService: ResponseService;

  /**
   * 导出数据到Excel
   * @param exportDto 导出参数
   * @returns 下载链接和文件信息
   */
  async exportToExcel(exportDto: BaseExportDTO): Promise<{
    downloadUrl: string;
    filename: string;
    fileId: string;
  }> {
    const workbook = new ExcelJS.Workbook();

    // 设置工作簿属性
    workbook.creator = '教师评价问卷系统';
    workbook.lastModifiedBy = '系统导出';
    workbook.created = new Date();
    workbook.modified = new Date();

    let filename = '';

    switch (exportDto.export_type) {
      case ExportType.SCHOOL_STATISTICS:
        await this.exportSchoolStatistics(
          workbook,
          exportDto as SchoolStatisticsExportDTO
        );
        filename = `学校统计报表_${exportDto.sso_school_code}_${
          exportDto.month || '全部'
        }.xlsx`;
        break;

      case ExportType.TEACHER_STATISTICS:
        await this.exportTeacherStatistics(
          workbook,
          exportDto as TeacherStatisticsExportDTO
        );
        filename = `教师统计报表_${
          (exportDto as TeacherStatisticsExportDTO).sso_teacher_id
        }_${exportDto.month || '全部'}.xlsx`;
        break;

      case ExportType.TEACHER_RANKING:
        await this.exportTeacherRanking(
          workbook,
          exportDto as TeacherRankingExportDTO
        );
        filename = `教师排名报表_${exportDto.sso_school_code}_${
          exportDto.month || '全部'
        }.xlsx`;
        break;

      case ExportType.QUESTIONNAIRE_RESPONSES:
        await this.exportQuestionnaireResponses(
          workbook,
          exportDto as QuestionnaireResponsesExportDTO
        );
        filename = `问卷响应数据_${exportDto.sso_school_code}_${
          exportDto.month || '全部'
        }.xlsx`;
        break;

      case ExportType.COMPREHENSIVE_REPORT:
        await this.exportComprehensiveReport(
          workbook,
          exportDto as ComprehensiveReportExportDTO
        );
        filename = `综合报表_${exportDto.sso_school_code}_${
          exportDto.month || '全部'
        }.xlsx`;
        break;

      case ExportType.INCOMPLETE_STUDENTS:
        await this.exportIncompleteStudents(
          workbook,
          exportDto as IncompleteStudentsExportDTO
        );
        filename = `未完成学生名单_${exportDto.sso_school_code}_${
          exportDto.month || '全部'
        }.xlsx`;
        break;

      default:
        throw new CustomError('不支持的导出类型');
    }

    const buffer = await workbook.xlsx.writeBuffer();

    // 生成唯一文件ID和临时文件路径
    const fileId = uuidv4();
    const tempFilename = `${fileId}_${filename}`;
    const publicDir = path.join(process.cwd(), 'public', 'temp');
    const filePath = path.join(publicDir, tempFilename);

    // 确保临时目录存在
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }

    // 写入临时文件
    fs.writeFileSync(filePath, buffer as Buffer);

    // 设置2小时后自动删除
    setTimeout(() => {
      this.deleteTemporaryFile(filePath);
    }, 2 * 60 * 60 * 1000); // 2小时

    // 生成下载链接
    const downloadUrl = `/public/temp/${tempFilename}`;

    return {
      downloadUrl,
      filename,
      fileId,
    };
  }

  /**
   * 导出学校统计数据
   */
  private async exportSchoolStatistics(
    workbook: ExcelJS.Workbook,
    exportDto: SchoolStatisticsExportDTO
  ): Promise<void> {
    // 优先尝试使用缓存的统计数据
    let statistics: any = null;

    // 如果指定了单个问卷ID，尝试使用缓存数据
    if (exportDto.questionnaire_id) {
      try {
        const cachedStats = await this.statisticsService.getCachedStatistics(
          exportDto.questionnaire_id
        );
        if (cachedStats && cachedStats.status === 'completed') {
          statistics = this.transformCachedToSchoolStatistics(
            cachedStats,
            exportDto
          );
          this.ctx.logger.info('使用缓存统计数据进行导出', {
            questionnaire_id: exportDto.questionnaire_id,
            cached_at: cachedStats.calculation_end_time,
          });
        }
      } catch (error) {
        this.ctx.logger.warn('获取缓存统计数据失败，使用实时计算', {
          questionnaire_id: exportDto.questionnaire_id,
          error: error.message,
        });
      }
    }

    // 如果没有缓存数据或不适用，使用实时计算
    if (!statistics) {
      statistics = await this.statisticsService.getSchoolStatistics({
        sso_school_code: exportDto.sso_school_code,
        month: exportDto.month,
        start_month: exportDto.start_month,
        end_month: exportDto.end_month,
        include_trend: exportDto.include_trend,
        include_teacher_ranking: exportDto.include_teacher_ranking,
      });
    }

    // 创建基础统计工作表
    const basicSheet = workbook.addWorksheet('学校基础统计');
    this.setupBasicSchoolStatisticsSheet(basicSheet, statistics);

    // 如果包含趋势数据，创建趋势工作表
    if (exportDto.include_trend && statistics.response_trend) {
      const trendSheet = workbook.addWorksheet('趋势分析');
      this.setupTrendSheet(trendSheet, statistics.response_trend);
    }

    // 如果包含教师排名，创建排名工作表
    if (exportDto.include_teacher_ranking && statistics.teacher_ranking) {
      const rankingSheet = workbook.addWorksheet('教师排名');
      this.setupTeacherRankingSheet(rankingSheet, statistics.teacher_ranking);
    }

    // 如果包含未完成学生统计，创建相应工作表
    if (
      exportDto.include_incomplete_students &&
      statistics.incomplete_students_summary
    ) {
      const incompleteSheet = workbook.addWorksheet('未完成统计');
      this.setupIncompleteStudentsSheet(
        incompleteSheet,
        statistics.incomplete_students_summary
      );
    }
  }

  /**
   * 导出教师统计数据
   */
  private async exportTeacherStatistics(
    workbook: ExcelJS.Workbook,
    exportDto: TeacherStatisticsExportDTO
  ): Promise<void> {
    // 获取教师统计数据
    const statistics = await this.statisticsService.getTeacherStatistics({
      sso_teacher_id: exportDto.sso_teacher_id,
      sso_school_code: exportDto.sso_school_code,
      month: exportDto.month,
      start_month: exportDto.start_month,
      end_month: exportDto.end_month,
      include_distribution: exportDto.include_distribution,
      include_keywords: exportDto.include_keywords,
      include_trend: exportDto.include_trend,
    });

    // 创建基础统计工作表
    const basicSheet = workbook.addWorksheet('教师基础统计');
    this.setupBasicTeacherStatisticsSheet(basicSheet, statistics);

    // 如果包含评分分布，创建分布工作表
    if (exportDto.include_distribution && statistics.score_distribution) {
      const distributionSheet = workbook.addWorksheet('评分分布');
      this.setupScoreDistributionSheet(
        distributionSheet,
        statistics.score_distribution
      );
    }

    // 如果包含关键词云，创建关键词工作表
    if (exportDto.include_keywords && statistics.keyword_cloud) {
      const keywordSheet = workbook.addWorksheet('关键词分析');
      this.setupKeywordSheet(keywordSheet, statistics.keyword_cloud);
    }

    // 如果包含趋势数据，创建趋势工作表
    if (exportDto.include_trend && statistics.evaluation_trend) {
      const trendSheet = workbook.addWorksheet('评价趋势');
      this.setupTeacherTrendSheet(trendSheet, statistics.evaluation_trend);
    }
  }

  /**
   * 导出教师排名数据
   */
  private async exportTeacherRanking(
    workbook: ExcelJS.Workbook,
    exportDto: TeacherRankingExportDTO
  ): Promise<void> {
    // 获取教师排名数据
    const ranking = await this.statisticsService.getTeacherRanking({
      sso_school_code: exportDto.sso_school_code,
      month: exportDto.month,
      subject: exportDto.subject,
      department: exportDto.department,
      page: 1,
      limit: exportDto.limit || 100,
      sort_by: exportDto.sort_by,
      sort_order: exportDto.sort_order,
    });

    // 创建排名工作表
    const rankingSheet = workbook.addWorksheet('教师排名');
    this.setupTeacherRankingSheet(rankingSheet, ranking.list);
  }

  /**
   * 设置基础学校统计工作表
   */
  private setupBasicSchoolStatisticsSheet(
    worksheet: ExcelJS.Worksheet,
    statistics: any
  ): void {
    // 设置标题
    worksheet.mergeCells('A1:F1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = '学校统计报表';
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    // 设置基础信息
    worksheet.getCell('A3').value = '学校编码:';
    worksheet.getCell('B3').value = statistics.sso_school_code;
    worksheet.getCell('A4').value = '学校名称:';
    worksheet.getCell('B4').value = statistics.sso_school_name;
    worksheet.getCell('A5').value = '统计月份:';
    worksheet.getCell('B5').value = statistics.month || '全部';

    // 设置统计数据表头
    const headers = ['指标名称', '数值', '单位', '说明'];

    const currentRow = 7;
    headers.forEach((header, index) => {
      const cell = worksheet.getCell(currentRow, index + 1);
      cell.value = header;
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };
    });

    // 添加统计数据
    const statsData = [
      ['总响应数', statistics.total_responses, '份', '所有问卷响应总数'],
      [
        '完成响应数',
        statistics.completed_responses,
        '份',
        '已完成的问卷响应数',
      ],
      ['学生总数', statistics.total_students, '人', '学校学生总人数'],
      ['完成率', statistics.completion_rate, '%', '基于学生总数的完成率'],
      [
        '学校平均分',
        statistics.school_average_score,
        '分',
        '学校整体评分平均值',
      ],
      [
        '教师平均分',
        statistics.teacher_average_score,
        '分',
        '所有教师评分平均值',
      ],
      [
        '被评价教师数',
        statistics.total_teachers_evaluated,
        '人',
        '被评价的教师总数',
      ],
    ];

    statsData.forEach((row, index) => {
      const rowIndex = currentRow + 1 + index;
      row.forEach((value, colIndex) => {
        worksheet.getCell(rowIndex, colIndex + 1).value = value;
      });
    });

    // 设置列宽
    worksheet.columns = [
      { width: 20 },
      { width: 15 },
      { width: 10 },
      { width: 30 },
    ];
  }

  /**
   * 设置趋势分析工作表
   */
  private setupTrendSheet(
    worksheet: ExcelJS.Worksheet,
    trendData: any[]
  ): void {
    // 设置标题
    worksheet.mergeCells('A1:F1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = '趋势分析';
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    // 设置表头
    const headers = [
      '月份',
      '总响应数',
      '完成响应数',
      '完成率(%)',
      '学校平均分',
      '教师平均分',
    ];

    const currentRow = 3;
    headers.forEach((header, index) => {
      const cell = worksheet.getCell(currentRow, index + 1);
      cell.value = header;
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };
    });

    // 添加数据
    trendData.forEach((item, index) => {
      const rowIndex = currentRow + 1 + index;
      const rowData = [
        item.month,
        item.total_responses,
        item.completed_responses,
        item.completion_rate,
        item.avg_school_score,
        item.avg_teacher_score,
      ];

      rowData.forEach((value, colIndex) => {
        worksheet.getCell(rowIndex, colIndex + 1).value = value;
      });
    });

    // 设置列宽
    worksheet.columns = [
      { width: 12 },
      { width: 12 },
      { width: 12 },
      { width: 12 },
      { width: 12 },
      { width: 12 },
    ];
  }

  /**
   * 设置教师排名工作表
   */
  private setupTeacherRankingSheet(
    worksheet: ExcelJS.Worksheet,
    rankingData: any[]
  ): void {
    // 设置标题
    worksheet.mergeCells('A1:G1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = '教师排名';
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    // 设置表头
    const headers = [
      '排名',
      '教师ID',
      '教师姓名',
      '科目',
      '部门',
      '平均分',
      '评价数量',
    ];

    const currentRow = 3;
    headers.forEach((header, index) => {
      const cell = worksheet.getCell(currentRow, index + 1);
      cell.value = header;
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };
    });

    // 添加数据
    rankingData.forEach((item, index) => {
      const rowIndex = currentRow + 1 + index;
      const rowData = [
        item.rank || index + 1,
        item.sso_teacher_id,
        item.sso_teacher_name,
        item.sso_teacher_subject,
        item.sso_teacher_department,
        item.average_score,
        item.evaluation_count,
      ];

      rowData.forEach((value, colIndex) => {
        worksheet.getCell(rowIndex, colIndex + 1).value = value;
      });
    });

    // 设置列宽
    worksheet.columns = [
      { width: 8 },  // 排名
      { width: 15 }, // 教师ID
      { width: 12 }, // 教师姓名
      { width: 12 }, // 科目
      { width: 15 }, // 部门
      { width: 10 }, // 平均分
      { width: 10 }, // 评价数量
    ];
  }

  /**
   * 设置未完成学生统计工作表
   */
  private setupIncompleteStudentsSheet(
    worksheet: ExcelJS.Worksheet,
    incompleteData: any
  ): void {
    // 设置标题
    worksheet.mergeCells('A1:E1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = '未完成学生统计';
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    let currentRow = 3;

    // 按年级统计
    if (incompleteData.by_grade && incompleteData.by_grade.length > 0) {
      worksheet.getCell(currentRow, 1).value = '按年级统计:';
      worksheet.getCell(currentRow, 1).font = { bold: true };
      currentRow += 2;

      const gradeHeaders = ['年级', '总学生数', '未完成数', '完成率(%)'];
      gradeHeaders.forEach((header, index) => {
        const cell = worksheet.getCell(currentRow, index + 1);
        cell.value = header;
        cell.font = { bold: true };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE0E0E0' },
        };
      });

      incompleteData.by_grade.forEach((grade, index) => {
        const rowIndex = currentRow + 1 + index;
        const rowData = [
          grade.grade_name,
          grade.total_students,
          grade.incomplete_count,
          grade.completion_rate,
        ];

        rowData.forEach((value, colIndex) => {
          worksheet.getCell(rowIndex, colIndex + 1).value = value;
        });
      });

      currentRow += incompleteData.by_grade.length + 3;
    }

    // 按班级统计
    if (incompleteData.by_class && incompleteData.by_class.length > 0) {
      worksheet.getCell(currentRow, 1).value = '按班级统计:';
      worksheet.getCell(currentRow, 1).font = { bold: true };
      currentRow += 2;

      const classHeaders = [
        '年级',
        '班级',
        '总学生数',
        '未完成数',
        '完成率(%)',
      ];
      classHeaders.forEach((header, index) => {
        const cell = worksheet.getCell(currentRow, index + 1);
        cell.value = header;
        cell.font = { bold: true };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE0E0E0' },
        };
      });

      incompleteData.by_class.forEach((classItem, index) => {
        const rowIndex = currentRow + 1 + index;
        const rowData = [
          classItem.grade_name,
          classItem.class_name,
          classItem.total_students,
          classItem.incomplete_count,
          classItem.completion_rate,
        ];

        rowData.forEach((value, colIndex) => {
          worksheet.getCell(rowIndex, colIndex + 1).value = value;
        });
      });
    }

    // 设置列宽
    worksheet.columns = [
      { width: 12 },
      { width: 12 },
      { width: 12 },
      { width: 12 },
      { width: 12 },
    ];
  }

  /**
   * 设置基础教师统计工作表
   */
  private setupBasicTeacherStatisticsSheet(
    worksheet: ExcelJS.Worksheet,
    statistics: any
  ): void {
    // 设置标题
    worksheet.mergeCells('A1:F1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = '教师统计报表';
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    // 设置基础信息
    worksheet.getCell('A3').value = '教师ID:';
    worksheet.getCell('B3').value = statistics.sso_teacher_id;
    worksheet.getCell('A4').value = '教师姓名:';
    worksheet.getCell('B4').value = statistics.sso_teacher_name;
    worksheet.getCell('A5').value = '科目:';
    worksheet.getCell('B5').value = statistics.sso_teacher_subject;
    worksheet.getCell('A6').value = '部门:';
    worksheet.getCell('B6').value = statistics.sso_teacher_department;
    worksheet.getCell('A7').value = '统计月份:';
    worksheet.getCell('B7').value = statistics.month || '全部';

    // 设置统计数据表头
    const headers = ['指标名称', '数值', '单位', '说明'];

    const currentRow = 9;
    headers.forEach((header, index) => {
      const cell = worksheet.getCell(currentRow, index + 1);
      cell.value = header;
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };
    });

    // 添加统计数据
    const statsData = [
      ['总评价数', statistics.total_evaluations, '次', '该教师收到的评价总数'],
      ['平均分', statistics.average_score, '分', '所有评价的平均分数'],
      ['推荐率', statistics.recommendation_rate, '%', '推荐该教师的比例'],
    ];

    // 添加详细评分
    if (statistics.detailed_scores) {
      statsData.push(
        [
          '教学质量',
          statistics.detailed_scores.teaching_quality,
          '分',
          '教学质量评分',
        ],
        [
          '教学态度',
          statistics.detailed_scores.teaching_attitude,
          '分',
          '教学态度评分',
        ],
        [
          '课堂管理',
          statistics.detailed_scores.classroom_management,
          '分',
          '课堂管理评分',
        ],
        [
          '沟通能力',
          statistics.detailed_scores.communication,
          '分',
          '沟通能力评分',
        ],
        [
          '专业知识',
          statistics.detailed_scores.professional_knowledge,
          '分',
          '专业知识评分',
        ]
      );
    }

    statsData.forEach((row, index) => {
      const rowIndex = currentRow + 1 + index;
      row.forEach((value, colIndex) => {
        worksheet.getCell(rowIndex, colIndex + 1).value = value;
      });
    });

    // 设置列宽
    worksheet.columns = [
      { width: 20 },
      { width: 15 },
      { width: 10 },
      { width: 30 },
    ];
  }

  /**
   * 设置评分分布工作表
   */
  private setupScoreDistributionSheet(
    worksheet: ExcelJS.Worksheet,
    distributionData: any[]
  ): void {
    // 设置标题
    worksheet.mergeCells('A1:D1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = '评分分布';
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    // 设置表头
    const headers = ['分数区间', '评价数量', '占比(%)', '说明'];

    const currentRow = 3;
    headers.forEach((header, index) => {
      const cell = worksheet.getCell(currentRow, index + 1);
      cell.value = header;
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };
    });

    // 添加数据
    distributionData.forEach((item, index) => {
      const rowIndex = currentRow + 1 + index;
      const rowData = [
        item.score_range,
        item.count,
        item.percentage,
        this.getScoreRangeDescription(item.score_range),
      ];

      rowData.forEach((value, colIndex) => {
        worksheet.getCell(rowIndex, colIndex + 1).value = value;
      });
    });

    // 设置列宽
    worksheet.columns = [
      { width: 12 },
      { width: 12 },
      { width: 12 },
      { width: 20 },
    ];
  }

  /**
   * 设置关键词工作表
   */
  private setupKeywordSheet(
    worksheet: ExcelJS.Worksheet,
    keywordData: any[]
  ): void {
    // 设置标题
    worksheet.mergeCells('A1:C1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = '关键词分析';
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    // 设置表头
    const headers = ['关键词', '出现次数', '频率排名'];

    const currentRow = 3;
    headers.forEach((header, index) => {
      const cell = worksheet.getCell(currentRow, index + 1);
      cell.value = header;
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };
    });

    // 添加数据
    keywordData.forEach((item, index) => {
      const rowIndex = currentRow + 1 + index;
      const rowData = [item.word, item.count, index + 1];

      rowData.forEach((value, colIndex) => {
        worksheet.getCell(rowIndex, colIndex + 1).value = value;
      });
    });

    // 设置列宽
    worksheet.columns = [{ width: 15 }, { width: 12 }, { width: 12 }];
  }

  /**
   * 设置教师趋势工作表
   */
  private setupTeacherTrendSheet(
    worksheet: ExcelJS.Worksheet,
    trendData: any[]
  ): void {
    // 设置标题
    worksheet.mergeCells('A1:C1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = '教师评价趋势';
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    // 设置表头
    const headers = ['月份', '评价数量', '平均分'];

    const currentRow = 3;
    headers.forEach((header, index) => {
      const cell = worksheet.getCell(currentRow, index + 1);
      cell.value = header;
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };
    });

    // 添加数据
    trendData.forEach((item, index) => {
      const rowIndex = currentRow + 1 + index;
      const rowData = [item.month, item.evaluation_count, item.average_score];

      rowData.forEach((value, colIndex) => {
        worksheet.getCell(rowIndex, colIndex + 1).value = value;
      });
    });

    // 设置列宽
    worksheet.columns = [{ width: 12 }, { width: 12 }, { width: 12 }];
  }

  /**
   * 导出问卷响应数据
   */
  private async exportQuestionnaireResponses(
    workbook: ExcelJS.Workbook,
    exportDto: QuestionnaireResponsesExportDTO
  ): Promise<void> {
    // 获取问卷响应数据
    const responses = await this.responseService.getResponseList({
      questionnaire_id: exportDto.questionnaire_id,
      parent_phone: exportDto.parent_phone,
      sso_student_code: exportDto.sso_student_code,
      month: exportDto.month,
      grade_code: exportDto.grade_code,
      class_code: exportDto.class_code,
      is_completed: exportDto.is_completed,
      page: 1,
      limit: 10000, // 导出大量数据
    });

    // 创建响应数据工作表
    const responseSheet = workbook.addWorksheet('问卷响应');
    this.setupQuestionnaireResponsesSheet(responseSheet, responses.list);

    // 如果包含答案详情，创建答案工作表
    if (exportDto.include_answers) {
      const answerSheet = workbook.addWorksheet('评价详情');
      this.setupAnswersSheet(answerSheet, responses.list);
    }
  }

  /**
   * 导出综合报表
   */
  private async exportComprehensiveReport(
    workbook: ExcelJS.Workbook,
    exportDto: ComprehensiveReportExportDTO
  ): Promise<void> {
    // 获取学校统计数据
    const schoolStats = await this.statisticsService.getSchoolStatistics({
      sso_school_code: exportDto.sso_school_code,
      month: exportDto.month,
      start_month: exportDto.start_month,
      end_month: exportDto.end_month,
      include_trend: exportDto.include_trend_analysis,
      include_teacher_ranking: exportDto.include_teacher_ranking,
    });

    // 创建学校概览工作表
    if (exportDto.include_school_summary) {
      const summarySheet = workbook.addWorksheet('学校概览');
      this.setupBasicSchoolStatisticsSheet(summarySheet, schoolStats);
    }

    // 创建教师排名工作表
    if (exportDto.include_teacher_ranking && schoolStats.teacher_ranking) {
      const rankingSheet = workbook.addWorksheet('教师排名');
      this.setupTeacherRankingSheet(rankingSheet, schoolStats.teacher_ranking);
    }

    // 创建趋势分析工作表
    if (exportDto.include_trend_analysis && schoolStats.response_trend) {
      const trendSheet = workbook.addWorksheet('趋势分析');
      this.setupTrendSheet(trendSheet, schoolStats.response_trend);
    }

    // 创建完成情况分析工作表
    if (
      exportDto.include_completion_analysis &&
      schoolStats.incomplete_students_summary
    ) {
      const completionSheet = workbook.addWorksheet('完成情况分析');
      this.setupIncompleteStudentsSheet(
        completionSheet,
        schoolStats.incomplete_students_summary
      );
    }
  }

  /**
   * 导出未完成学生名单
   */
  private async exportIncompleteStudents(
    workbook: ExcelJS.Workbook,
    exportDto: IncompleteStudentsExportDTO
  ): Promise<void> {
    // 获取未完成学生数据
    const incompleteStudents =
      await this.statisticsService.getIncompleteStudentsWithPagination(
        exportDto.sso_school_code,
        exportDto.questionnaire_id,
        exportDto.month,
        1,
        10000,
        exportDto.grade_code,
        exportDto.class_code
      );

    // 创建未完成学生名单工作表
    const studentsSheet = workbook.addWorksheet('未完成学生名单');

    // 从班级数据中提取学生列表
    const studentsList: any[] = [];
    incompleteStudents.classes.forEach(classData => {
      if (classData.students) {
        studentsList.push(...classData.students);
      }
    });

    this.setupIncompleteStudentsListSheet(
      studentsSheet,
      studentsList,
      exportDto.include_contact_info
    );

    // 创建统计汇总工作表
    const summarySheet = workbook.addWorksheet('统计汇总');
    this.setupIncompleteStudentsSheet(summarySheet, {
      by_grade: incompleteStudents.summary.by_grade || [],
      by_class: incompleteStudents.summary.by_class || [],
    });
  }

  /**
   * 设置问卷响应工作表
   */
  private setupQuestionnaireResponsesSheet(
    worksheet: ExcelJS.Worksheet,
    responses: any[]
  ): void {
    // 设置标题
    worksheet.mergeCells('A1:L1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = '问卷响应数据';
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    // 设置表头
    const headers = [
      '响应ID',
      '问卷ID',
      '家长手机号',
      '家长姓名',
      '学生编码',
      '学生姓名',
      '学生班级',
      '学生年级',
      '月份',
      '学校评分',
      '教师平均分',
      '提交时间',
    ];

    const currentRow = 3;
    headers.forEach((header, index) => {
      const cell = worksheet.getCell(currentRow, index + 1);
      cell.value = header;
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };
    });

    // 添加数据
    responses.forEach((response, index) => {
      const rowIndex = currentRow + 1 + index;
      const rowData = [
        response.id,
        response.questionnaire_id,
        response.parent_phone,
        response.parent_name,
        response.sso_student_code,
        response.sso_student_name,
        response.sso_student_class,
        response.sso_student_grade,
        response.month,
        response.school_rating,
        response.total_average_score,
        response.created_at,
      ];

      rowData.forEach((value, colIndex) => {
        worksheet.getCell(rowIndex, colIndex + 1).value = value;
      });
    });

    // 设置列宽
    worksheet.columns = [
      { width: 10 },
      { width: 10 },
      { width: 15 },
      { width: 12 },
      { width: 15 },
      { width: 12 },
      { width: 12 },
      { width: 12 },
      { width: 10 },
      { width: 10 },
      { width: 12 },
      { width: 20 },
    ];
  }

  /**
   * 设置答案详情工作表
   */
  private setupAnswersSheet(
    worksheet: ExcelJS.Worksheet,
    responses: any[]
  ): void {
    // 设置标题
    worksheet.mergeCells('A1:I1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = '教师评价详情';
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    // 设置表头
    const headers = [
      '响应ID',
      '教师ID',
      '教师姓名',
      '教师科目',
      '教师职位',
      '教师部门',
      '评分',
      '评价描述',
      '提交时间',
    ];

    const currentRow = 3;
    headers.forEach((header, index) => {
      const cell = worksheet.getCell(currentRow, index + 1);
      cell.value = header;
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };
    });

    // 添加数据
    let dataRowIndex = currentRow + 1;
    responses.forEach(response => {
      if (response.answers && response.answers.length > 0) {
        response.answers.forEach(answer => {
          const rowData = [
            response.id,
            answer.sso_teacher_id,
            answer.sso_teacher_name,
            answer.sso_teacher_subject,
            answer.sso_teacher_position,
            answer.sso_teacher_department,
            answer.rating,
            answer.description,
            answer.created_at,
          ];

          rowData.forEach((value, colIndex) => {
            worksheet.getCell(dataRowIndex, colIndex + 1).value = value;
          });

          dataRowIndex++;
        });
      }
    });

    // 设置列宽
    worksheet.columns = [
      { width: 10 },
      { width: 15 },
      { width: 12 },
      { width: 12 },
      { width: 12 },
      { width: 15 },
      { width: 10 },
      { width: 30 },
      { width: 20 },
    ];
  }

  /**
   * 设置未完成学生名单工作表
   */
  private setupIncompleteStudentsListSheet(
    worksheet: ExcelJS.Worksheet,
    students: any[],
    includeContactInfo = false
  ): void {
    // 设置标题
    const titleCells = includeContactInfo ? 'A1:H1' : 'A1:F1';
    worksheet.mergeCells(titleCells);
    const titleCell = worksheet.getCell('A1');
    titleCell.value = '未完成学生名单';
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };

    // 设置表头
    const headers = [
      '学生编码',
      '学生姓名',
      '年级',
      '班级',
      '年级编码',
      '班级编码',
    ];

    if (includeContactInfo) {
      headers.push('联系电话', '家长姓名');
    }

    const currentRow = 3;
    headers.forEach((header, index) => {
      const cell = worksheet.getCell(currentRow, index + 1);
      cell.value = header;
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };
    });

    // 添加数据
    students.forEach((student, index) => {
      const rowIndex = currentRow + 1 + index;
      const rowData = [
        student.sso_student_code,
        student.sso_student_name,
        student.grade_name,
        student.class_name,
        student.grade_code,
        student.class_code,
      ];

      if (includeContactInfo) {
        rowData.push(student.contact_phone || '', student.parent_name || '');
      }

      rowData.forEach((value, colIndex) => {
        worksheet.getCell(rowIndex, colIndex + 1).value = value;
      });
    });

    // 设置列宽
    const columnWidths = [
      { width: 15 },
      { width: 12 },
      { width: 10 },
      { width: 10 },
      { width: 10 },
      { width: 10 },
    ];

    if (includeContactInfo) {
      columnWidths.push({ width: 15 }, { width: 12 });
    }

    worksheet.columns = columnWidths;
  }

  /**
   * 获取分数区间描述
   */
  private getScoreRangeDescription(scoreRange: string): string {
    const descriptions = {
      '90-100': '优秀',
      '80-89': '良好',
      '70-79': '中等',
      '60-69': '及格',
      '60以下': '不及格',
    };
    return descriptions[scoreRange] || '';
  }

  /**
   * 将缓存的统计数据转换为学校统计格式
   */
  private transformCachedToSchoolStatistics(
    cachedStats: any,
    exportDto: SchoolStatisticsExportDTO
  ): any {
    // 解析缓存的统计数据
    const schoolStats = cachedStats.school_statistics || {};
    const gradeStats = cachedStats.grade_statistics || [];
    const teacherStats = cachedStats.teacher_statistics || [];

    // 构建学校统计数据结构
    const result: any = {
      sso_school_code: cachedStats.sso_school_code,
      sso_school_name: schoolStats.sso_school_name || '',
      month: cachedStats.month,
      total_responses: schoolStats.total_responses || 0,
      completed_responses: schoolStats.completed_responses || 0,
      total_students: schoolStats.total_students || 0,
      completion_rate: schoolStats.completion_rate || 0,
      school_average_score: schoolStats.school_average_score || 0,
      teacher_average_score: schoolStats.teacher_average_score || 0,
      total_teachers_evaluated: teacherStats.length || 0,
      cached_data: true,
      cached_at: cachedStats.calculation_end_time,
    };

    // 如果需要教师排名数据
    if (exportDto.include_teacher_ranking && teacherStats.length > 0) {
      result.teacher_ranking = teacherStats
        .sort((a, b) => (b.average_score || 0) - (a.average_score || 0))
        .slice(0, 50) // 限制前50名
        .map((teacher, index) => ({
          rank: index + 1,
          sso_teacher_id: teacher.sso_teacher_id,
          sso_teacher_name: teacher.sso_teacher_name,
          sso_teacher_subject: teacher.sso_teacher_subject,
          sso_teacher_department: teacher.sso_teacher_department,
          average_score: teacher.average_score,
          evaluation_count: teacher.evaluation_count,
          recommendation_rate: teacher.recommendation_rate,
        }));
    }

    // 如果需要趋势数据（缓存数据通常是单月的，所以趋势数据可能有限）
    if (exportDto.include_trend) {
      result.response_trend = [
        {
          month: cachedStats.month,
          total_responses: schoolStats.total_responses || 0,
          completed_responses: schoolStats.completed_responses || 0,
          completion_rate: schoolStats.completion_rate || 0,
          avg_school_score: schoolStats.school_average_score || 0,
          avg_teacher_score: schoolStats.teacher_average_score || 0,
        },
      ];
    }

    // 如果需要未完成学生统计
    if (exportDto.include_incomplete_students && gradeStats.length > 0) {
      result.incomplete_students_summary = {
        by_grade: gradeStats.map(grade => ({
          grade_name: grade.grade_name,
          total_students: grade.total_students,
          incomplete_count: grade.total_students - grade.completed_responses,
          completion_rate: grade.completion_rate,
        })),
        by_class: [], // 缓存数据中可能没有班级级别的详细数据
      };
    }

    return result;
  }

  /**
   * 删除临时文件
   * @param filePath 文件路径
   */
  private deleteTemporaryFile(filePath: string): void {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        this.ctx.logger.info('临时文件已删除', { filePath });
      }
    } catch (error) {
      this.ctx.logger.error('删除临时文件失败', error, { filePath });
    }
  }
}
