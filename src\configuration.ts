import { Configuration, App } from '@midwayjs/core';
import * as koa from '@midwayjs/koa';
import * as validate from '@midwayjs/validate';
import * as info from '@midwayjs/info';
import * as staticFile from '@midwayjs/static-file';
import { join } from 'path';
import { DefaultErrorFilter } from './filter/default.filter';
import { OperationLogMiddleware } from './middleware/operation-log.middleware';
import { RequestLoggerMiddleware } from './middleware/requestlogger.middleware';
import { FormatMiddleware } from './middleware/format.middleware';
// 导入JWT认证组件
import jwtComponent from './component/jwt-component';
import * as sequelize from '@midwayjs/sequelize';
import { FileCleanupService } from './service/file-cleanup.service';

@Configuration({
  imports: [
    koa,
    validate,
    staticFile, // 静态文件服务
    jwtComponent, // 引入JWT认证组件（自动管理jwt和axios依赖）
    sequelize,
    {
      component: info,
      enabledEnvironment: ['local'],
    },
  ],
  importConfigs: [join(__dirname, './config')],
})
export class MainConfiguration {
  @App('koa')
  app: koa.Application;

  async onReady() {
    // add middleware
    this.app.useMiddleware([
      RequestLoggerMiddleware,
      OperationLogMiddleware,
      FormatMiddleware,
    ]);
    // add filter
    this.app.useFilter([DefaultErrorFilter]);

    // 初始化文件清理服务
    await this.app.getApplicationContext().getAsync(FileCleanupService);
    console.log('文件清理服务已启动');
  }
}
