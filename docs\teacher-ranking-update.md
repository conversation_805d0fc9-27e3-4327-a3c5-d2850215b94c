# 教师排名功能更新说明

## 📋 更新内容

根据需求，已从学校统计报表中的教师排名部分移除推荐率显示。

## 🔧 具体修改

### 1. 导出服务修改 (`src/service/export.service.ts`)

#### 表头调整
```typescript
// 修改前
const headers = [
  '排名', '教师ID', '教师姓名', '科目', '部门', '平均分', '评价数量', '推荐率(%)'
];

// 修改后  
const headers = [
  '排名', '教师ID', '教师姓名', '科目', '部门', '平均分', '评价数量'
];
```

#### 标题合并单元格调整
```typescript
// 修改前
worksheet.mergeCells('A1:H1');

// 修改后
worksheet.mergeCells('A1:G1');
```

#### 数据行调整
```typescript
// 修改前
const rowData = [
  item.rank || index + 1,
  item.sso_teacher_id,
  item.sso_teacher_name,
  item.sso_teacher_subject,
  item.sso_teacher_department,
  item.average_score,
  item.evaluation_count,
  item.recommendation_rate, // 移除此字段
];

// 修改后
const rowData = [
  item.rank || index + 1,
  item.sso_teacher_id,
  item.sso_teacher_name,
  item.sso_teacher_subject,
  item.sso_teacher_department,
  item.average_score,
  item.evaluation_count,
];
```

#### 列宽设置调整
```typescript
// 修改前
worksheet.columns = [
  { width: 8 }, { width: 15 }, { width: 12 }, { width: 12 },
  { width: 15 }, { width: 10 }, { width: 10 }, { width: 12 }
];

// 修改后
worksheet.columns = [
  { width: 8 },  // 排名
  { width: 15 }, // 教师ID
  { width: 12 }, // 教师姓名
  { width: 12 }, // 科目
  { width: 15 }, // 部门
  { width: 10 }, // 平均分
  { width: 10 }, // 评价数量
];
```

### 2. DTO验证修改 (`src/dto/export.dto.ts`)

#### 排序字段限制
```typescript
// 修改前
@Rule(
  RuleType.string()
    .valid('average_score', 'evaluation_count', 'recommendation_rate')
    .default('average_score')
    .messages({
      'any.only': '排序字段只能是average_score、evaluation_count或recommendation_rate',
    })
)

// 修改后
@Rule(
  RuleType.string()
    .valid('average_score', 'evaluation_count')
    .default('average_score')
    .messages({
      'any.only': '排序字段只能是average_score或evaluation_count',
    })
)
```

### 3. 文档更新

#### API文档 (`docs/export-api.md`)
- 更新排序字段说明，移除 `recommendation_rate` 选项

#### 前端文档 (`docs/export-api-frontend.md`)
- 更新示例代码中的排序字段注释

#### 测试页面 (`test-export.html`)
- 更新测试代码中的排序字段注释

## 📊 影响范围

### 受影响的功能
1. **学校统计导出** - 教师排名工作表
2. **教师排名导出** - 专门的教师排名导出
3. **综合报表导出** - 包含教师排名的部分

### 不受影响的功能
- 教师个人统计导出
- 问卷响应导出
- 未完成学生导出
- 其他统计功能（推荐率数据仍然存在，只是不在排名表中显示）

## 🔍 验证方法

### 1. 导出测试
```javascript
// 测试学校统计导出
const response = await fetch('/api/export/school-statistics', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    export_type: 'school_statistics',
    sso_school_code: 'SCHOOL001',
    include_teacher_ranking: true
  })
});
```

### 2. 检查Excel文件
- 打开导出的Excel文件
- 查看"教师排名"工作表
- 确认只有7列：排名、教师ID、教师姓名、科目、部门、平均分、评价数量
- 确认没有推荐率列

### 3. 排序功能测试
```javascript
// 测试教师排名导出的排序功能
const response = await fetch('/api/export/teacher-ranking', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    export_type: 'teacher_ranking',
    sso_school_code: 'SCHOOL001',
    sort_by: 'evaluation_count', // 只能使用 average_score 或 evaluation_count
    sort_order: 'DESC'
  })
});
```

## 📝 注意事项

1. **数据完整性**: 推荐率数据仍然在数据库中存在，只是不在导出的排名表中显示
2. **API兼容性**: 如果前端代码中使用了 `recommendation_rate` 作为排序字段，需要修改为 `average_score` 或 `evaluation_count`
3. **向后兼容**: 旧的导出文件格式已改变，新导出的文件将不包含推荐率列

## 🚀 部署建议

1. **测试验证**: 在测试环境中验证所有导出功能正常
2. **前端更新**: 确保前端代码不再使用 `recommendation_rate` 排序
3. **用户通知**: 如有必要，通知用户导出格式的变更
4. **文档同步**: 确保所有相关文档都已更新

## 📋 总结

此次更新简化了教师排名的显示内容，移除了推荐率列，使排名表更加简洁明了。修改涉及导出服务、数据验证、文档和测试代码，确保了功能的完整性和一致性。
