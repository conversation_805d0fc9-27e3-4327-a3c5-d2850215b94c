# 导出功能API文档

## 概述

教师评价问卷系统提供完整的Excel导出功能，支持多种数据类型的导出，包括学校统计、教师统计、教师排名、问卷响应、综合报表和未完成学生名单等。

## 功能特点

- 📊 **多工作表**: 根据数据类型自动创建多个工作表
- 🎨 **美观格式**: 自动设置表头样式、列宽、数据格式
- 📈 **完整数据**: 包含统计分析、趋势数据、排名信息等
- 🔍 **灵活筛选**: 支持按学校、月份、教师等多维度筛选
- ⚡ **性能优化**: 优先使用缓存统计数据，提升导出速度
- 🔄 **智能切换**: 自动在缓存数据和实时计算之间切换

## API接口列表

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 学校统计导出 | POST | `/api/export/school-statistics` | 导出学校统计数据 |
| 教师统计导出 | POST | `/api/export/teacher-statistics` | 导出教师统计数据 |
| 教师排名导出 | POST | `/api/export/teacher-ranking` | 导出教师排名数据 |
| 问卷响应导出 | POST | `/api/export/questionnaire-responses` | 导出问卷响应数据 |
| 综合报表导出 | POST | `/api/export/comprehensive-report` | 导出综合报表 |
| 未完成学生导出 | POST | `/api/export/incomplete-students` | 导出未完成学生名单 |
| 通用导出接口 | POST | `/api/export/general` | 通用导出接口 |

## 接口详情

### 1. 学校统计导出

**URL**: `POST /api/export/school-statistics`

**请求参数**:
```json
{
  "export_type": "school_statistics",
  "sso_school_code": "SCHOOL001",
  "questionnaire_id": 123,
  "month": "2024-03",
  "start_month": "2024-01",
  "end_month": "2024-03",
  "export_format": "xlsx",
  "include_trend": true,
  "include_teacher_ranking": true,
  "include_incomplete_students": true
}
```

**参数说明**:
- `export_type`: 导出类型，固定值"school_statistics"（必填）
- `sso_school_code`: 学校编码（必填）
- `questionnaire_id`: 问卷ID（可选，提供时优先使用缓存数据）
- `month`: 统计月份，格式YYYY-MM（可选）
- `start_month`: 开始月份（可选）
- `end_month`: 结束月份（可选）
- `export_format`: 导出格式，固定为xlsx（可省略）
- `include_trend`: 是否包含趋势分析（可选）
- `include_teacher_ranking`: 是否包含教师排名（可选）
- `include_incomplete_students`: 是否包含未完成统计（可选）

**响应**: 直接返回Excel文件流

**工作表内容**:
- 学校基础统计（必含）
- 趋势分析（可选）
- 教师排名（可选）
- 未完成统计（可选）

### 2. 教师统计导出

**URL**: `POST /api/export/teacher-statistics`

**请求参数**:
```json
{
  "export_type": "teacher_statistics",
  "sso_school_code": "SCHOOL001",
  "sso_teacher_id": "TEACHER001",
  "month": "2024-03",
  "start_month": "2024-01",
  "end_month": "2024-03",
  "export_format": "xlsx",
  "include_distribution": true,
  "include_keywords": true,
  "include_trend": true
}
```

**参数说明**:
- `export_type`: 导出类型，固定值"teacher_statistics"（必填）
- `sso_school_code`: 学校编码（必填）
- `sso_teacher_id`: 教师ID（必填）
- `month`: 统计月份（可选）
- `start_month`: 开始月份（可选）
- `end_month`: 结束月份（可选）
- `export_format`: 导出格式，固定为xlsx（可省略）
- `include_distribution`: 是否包含评分分布（可选）
- `include_keywords`: 是否包含关键词分析（可选）
- `include_trend`: 是否包含评价趋势（可选）

**工作表内容**:
- 教师基础统计（必含）
- 评分分布（可选）
- 关键词分析（可选）
- 评价趋势（可选）

### 3. 教师排名导出

**URL**: `POST /api/export/teacher-ranking`

**请求参数**:
```json
{
  "export_type": "teacher_ranking",
  "sso_school_code": "SCHOOL001",
  "month": "2024-03",
  "subject": "数学",
  "department": "初中部",
  "sort_by": "average_score",
  "sort_order": "DESC",
  "limit": 100,
  "export_format": "xlsx"
}
```

**参数说明**:
- `export_type`: 导出类型，固定值"teacher_ranking"（必填）
- `sso_school_code`: 学校编码（必填）
- `month`: 统计月份（可选）
- `subject`: 科目筛选（可选）
- `department`: 部门筛选（可选）
- `sort_by`: 排序字段，可选值：`average_score`、`evaluation_count`、`recommendation_rate`，默认`average_score`（可选）
- `sort_order`: 排序方向，可选值：`ASC`、`DESC`，默认`DESC`（可选）
- `limit`: 导出数量，范围1-1000，默认100（可选）
- `export_format`: 导出格式，固定为xlsx（可省略）

**工作表内容**:
- 教师排名（必含）

### 4. 问卷响应导出

**URL**: `POST /api/export/questionnaire-responses`

**请求参数**:
```json
{
  "export_type": "questionnaire_responses",
  "sso_school_code": "SCHOOL001",
  "questionnaire_id": 1,
  "month": "2024-03",
  "parent_phone": "13800138000",
  "sso_student_code": "STU001",
  "grade_code": "7",
  "class_code": "1",
  "is_completed": true,
  "include_answers": true,
  "export_format": "xlsx"
}
```

**参数说明**:
- `export_type`: 导出类型，固定值"questionnaire_responses"（必填）
- `sso_school_code`: 学校编码（必填）
- `questionnaire_id`: 问卷ID（可选）
- `month`: 统计月份（可选）
- `parent_phone`: 家长手机号筛选（可选）
- `sso_student_code`: 学生编码筛选（可选）
- `grade_code`: 年级编码筛选（可选）
- `class_code`: 班级编码筛选（可选）
- `is_completed`: 是否已完成筛选（可选）
- `include_answers`: 是否包含答案详情（可选）
- `export_format`: 导出格式，固定为xlsx（可省略）

**工作表内容**:
- 问卷响应（必含）
- 评价详情（可选）

### 5. 综合报表导出

**URL**: `POST /api/export/comprehensive-report`

**请求参数**:
```json
{
  "export_type": "comprehensive_report",
  "sso_school_code": "SCHOOL001",
  "month": "2024-03",
  "start_month": "2024-01",
  "end_month": "2024-03",
  "export_format": "xlsx",
  "include_school_summary": true,
  "include_teacher_ranking": true,
  "include_grade_analysis": true,
  "include_subject_analysis": true,
  "include_trend_analysis": true,
  "include_completion_analysis": true
}
```

**参数说明**:
- `export_type`: 导出类型，固定值"comprehensive_report"（必填）
- `sso_school_code`: 学校编码（必填）
- `month`: 统计月份（可选）
- `start_month`: 开始月份（可选）
- `end_month`: 结束月份（可选）
- `export_format`: 导出格式，固定为xlsx（可省略）
- `include_school_summary`: 是否包含学校概览（可选）
- `include_teacher_ranking`: 是否包含教师排名（可选）
- `include_grade_analysis`: 是否包含年级分析（可选）
- `include_subject_analysis`: 是否包含科目分析（可选）
- `include_trend_analysis`: 是否包含趋势分析（可选）
- `include_completion_analysis`: 是否包含完成情况分析（可选）

**工作表内容**:
- 学校概览（可选）
- 教师排名（可选）
- 年级分析（可选）
- 科目分析（可选）
- 趋势分析（可选）
- 完成情况分析（可选）

### 6. 未完成学生导出

**URL**: `POST /api/export/incomplete-students`

**请求参数**:
```json
{
  "export_type": "incomplete_students",
  "sso_school_code": "SCHOOL001",
  "questionnaire_id": 1,
  "month": "2024-03",
  "grade_code": "7",
  "class_code": "1",
  "include_contact_info": true,
  "export_format": "xlsx"
}
```

**参数说明**:
- `export_type`: 导出类型，固定值"incomplete_students"（必填）
- `sso_school_code`: 学校编码（必填）
- `questionnaire_id`: 问卷ID（可选）
- `month`: 统计月份（可选）
- `grade_code`: 年级编码筛选（可选）
- `class_code`: 班级编码筛选（可选）
- `include_contact_info`: 是否包含联系信息（可选）
- `export_format`: 导出格式，固定为xlsx（可省略）

**工作表内容**:
- 未完成学生名单（必含）
- 统计汇总（必含）

### 7. 通用导出接口

**URL**: `POST /api/export/general`

**请求参数**:
```json
{
  "export_type": "school_statistics",
  "sso_school_code": "SCHOOL001",
  "month": "2024-03",
  "export_format": "xlsx"
}
```

**参数说明**:
- `export_type`: 导出类型（必填）
- `sso_school_code`: 学校编码（必填）
- `month`: 统计月份（可选）
- `export_format`: 导出格式，固定为xlsx（可省略）
- 其他参数根据导出类型而定，参考对应接口

**支持的导出类型**:
- `school_statistics`: 学校统计
- `teacher_statistics`: 教师统计
- `teacher_ranking`: 教师排名
- `questionnaire_responses`: 问卷响应
- `comprehensive_report`: 综合报表
- `incomplete_students`: 未完成学生

## 通用响应格式

### 成功响应
成功时直接返回Excel文件流，浏览器会自动下载文件。

**响应头**:
- `Content-Type`: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- `Content-Disposition`: `attachment; filename="文件名.xlsx"`
- `Access-Control-Expose-Headers`: `Content-Disposition`

**响应体**: 直接是Excel文件的二进制数据流

### 错误响应
失败时返回JSON格式错误信息：
```json
{
  "errCode": 500,
  "msg": "导出失败的具体原因",
  "data": null
}
```

### 重要修复说明
系统已修复FormatMiddleware导致的文件下载问题：
- 现在文件下载接口会直接返回文件流，不会被包装成JSON格式
- 中间件会检查Content-Type和Content-Disposition头来识别文件下载响应
- 确保浏览器能正确触发文件下载行为

## 性能优化说明

### 缓存数据优先
- 学校统计导出支持缓存数据优先策略
- 当提供 `questionnaire_id` 参数时，系统优先使用预计算的缓存统计数据
- 缓存数据导出速度更快，适合大数据量场景
- 如果缓存数据不可用或未完成计算，会自动切换到实时计算模式

### 推荐使用场景
- **定期报表**: 月度、季度报表建议使用缓存数据
- **大数据量**: 学生数 > 1000 的学校建议使用缓存数据
- **批量导出**: 需要导出多个报表时优先使用缓存数据
- **高并发**: 多用户同时导出时使用缓存数据

## 前端调用示例

### JavaScript/Fetch示例

```javascript
// 导出学校统计数据
async function exportSchoolStatistics() {
  const requestData = {
    export_type: 'school_statistics',
    sso_school_code: 'SCHOOL001',
    questionnaire_id: 123, // 可选，提供时使用缓存数据
    month: '2024-03',
    include_trend: true,
    include_teacher_ranking: true
  };

  try {
    const response = await fetch('/api/export/school-statistics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (response.ok) {
      const blob = await response.blob();

      // 获取文件名
      const contentDisposition = response.headers.get('Content-Disposition');
      const filename = contentDisposition
        ? decodeURIComponent(contentDisposition.split('filename=')[1].replace(/"/g, ''))
        : '学校统计报表.xlsx';

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } else {
      const errorData = await response.json();
      throw new Error(errorData.msg || '导出失败');
    }
  } catch (error) {
    console.error('导出失败:', error);
    alert('导出失败: ' + error.message);
  }
}

// 导出教师排名数据
async function exportTeacherRanking() {
  const requestData = {
    export_type: 'teacher_ranking',
    sso_school_code: 'SCHOOL001',
    month: '2024-03',
    sort_by: 'average_score',
    sort_order: 'DESC',
    limit: 50
  };

  try {
    const response = await fetch('/api/export/teacher-ranking', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = '教师排名报表.xlsx';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } else {
      const errorData = await response.json();
      throw new Error(errorData.msg || '导出失败');
    }
  } catch (error) {
    console.error('导出失败:', error);
    alert('导出失败: ' + error.message);
  }
}
```

### Axios示例

```javascript
import axios from 'axios';

// 导出综合报表
async function exportComprehensiveReport() {
  const requestData = {
    export_type: 'comprehensive_report',
    sso_school_code: 'SCHOOL001',
    month: '2024-03',
    include_school_summary: true,
    include_teacher_ranking: true,
    include_trend_analysis: true
  };

  try {
    const response = await axios.post('/api/export/comprehensive-report', requestData, {
      responseType: 'blob'
    });

    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', '综合报表.xlsx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('导出失败:', error);
    alert('导出失败: ' + (error.response?.data?.msg || error.message));
  }
}
```

## 注意事项

### 性能相关
1. **响应时间**: 大量数据导出时可能需要较长时间，建议前端设置合理的超时时间（建议30秒以上）
2. **缓存优先**: 对于大数据量场景，建议使用带 `questionnaire_id` 参数的缓存数据导出
3. **并发限制**: 避免同时发起多个导出请求，可能导致服务器过载

### 前端处理
1. **错误处理**: 必须正确处理导出失败的情况，向用户显示友好的错误信息
2. **文件名处理**: 响应头中的文件名已进行URL编码，前端需要正确解码
3. **进度提示**: 建议在导出过程中显示加载提示，提升用户体验

### 数据安全
1. **权限验证**: 实际使用时应添加适当的权限验证，确保用户只能导出有权限的数据
2. **参数验证**: 前端应对用户输入进行基本验证，避免无效请求

## 常见问题

### Q: 导出时间过长怎么办？
A: 建议使用缓存数据导出（提供questionnaire_id参数），或者缩小导出范围（如按月份、年级筛选）。

### Q: 如何判断是否使用了缓存数据？
A: 查看服务器日志，使用缓存数据时会有相应的日志记录。

### Q: 导出文件为空或格式错误？
A: 检查请求参数是否正确，特别是学校编码和筛选条件；查看错误响应中的具体错误信息。

### Q: 支持哪些浏览器？
A: 支持所有现代浏览器，包括Chrome、Firefox、Safari、Edge等。
