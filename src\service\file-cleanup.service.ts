import { Provide, Inject, Init } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import * as fs from 'fs';
import * as path from 'path';

@Provide()
export class FileCleanupService {
  @Inject()
  ctx: Context;

  private cleanupInterval: NodeJS.Timeout;
  private readonly CLEANUP_INTERVAL = 2 * 60 * 60 * 1000; // 2小时
  private readonly FILE_MAX_AGE = 2 * 60 * 60 * 1000; // 2小时

  @Init()
  async init() {
    // 启动定时清理任务
    this.startCleanupTask();
    this.ctx.logger.info('文件清理定时任务已启动，每2小时执行一次');
  }

  /**
   * 启动定时清理任务
   */
  private startCleanupTask(): void {
    // 立即执行一次清理
    this.cleanupExpiredFiles();

    // 设置定时任务，每2小时执行一次
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredFiles();
    }, this.CLEANUP_INTERVAL);
  }

  /**
   * 停止定时清理任务
   */
  public stopCleanupTask(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.ctx.logger.info('文件清理定时任务已停止');
    }
  }

  /**
   * 清理过期的临时文件
   */
  public cleanupExpiredFiles(): void {
    try {
      const tempDir = path.join(process.cwd(), 'public', 'temp');
      
      // 检查临时目录是否存在
      if (!fs.existsSync(tempDir)) {
        this.ctx.logger.warn('临时文件目录不存在', { tempDir });
        return;
      }

      const files = fs.readdirSync(tempDir);
      const now = Date.now();
      let deletedCount = 0;
      let totalSize = 0;

      files.forEach(filename => {
        const filePath = path.join(tempDir, filename);
        
        try {
          const stats = fs.statSync(filePath);
          
          // 检查文件是否为普通文件（排除目录）
          if (!stats.isFile()) {
            return;
          }

          // 计算文件年龄
          const fileAge = now - stats.mtime.getTime();
          
          // 如果文件超过2小时，则删除
          if (fileAge > this.FILE_MAX_AGE) {
            const fileSize = stats.size;
            fs.unlinkSync(filePath);
            deletedCount++;
            totalSize += fileSize;
            
            this.ctx.logger.info('已删除过期临时文件', {
              filename,
              fileAge: Math.round(fileAge / 1000 / 60), // 转换为分钟
              fileSize: this.formatFileSize(fileSize)
            });
          }
        } catch (error) {
          this.ctx.logger.error('处理文件时出错', error, { filename, filePath });
        }
      });

      // 记录清理结果
      if (deletedCount > 0) {
        this.ctx.logger.info('临时文件清理完成', {
          deletedCount,
          totalSize: this.formatFileSize(totalSize),
          remainingFiles: files.length - deletedCount
        });
      } else {
        this.ctx.logger.debug('没有需要清理的过期文件', {
          totalFiles: files.length
        });
      }

    } catch (error) {
      this.ctx.logger.error('清理临时文件时发生错误', error);
    }
  }

  /**
   * 手动清理指定文件
   * @param filename 文件名
   */
  public deleteFile(filename: string): boolean {
    try {
      const filePath = path.join(process.cwd(), 'public', 'temp', filename);
      
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        fs.unlinkSync(filePath);
        
        this.ctx.logger.info('手动删除临时文件', {
          filename,
          fileSize: this.formatFileSize(stats.size)
        });
        
        return true;
      } else {
        this.ctx.logger.warn('要删除的文件不存在', { filename });
        return false;
      }
    } catch (error) {
      this.ctx.logger.error('删除文件失败', error, { filename });
      return false;
    }
  }

  /**
   * 获取临时文件目录状态
   */
  public getTempDirStatus(): {
    totalFiles: number;
    totalSize: number;
    expiredFiles: number;
    expiredSize: number;
  } {
    try {
      const tempDir = path.join(process.cwd(), 'public', 'temp');
      
      if (!fs.existsSync(tempDir)) {
        return { totalFiles: 0, totalSize: 0, expiredFiles: 0, expiredSize: 0 };
      }

      const files = fs.readdirSync(tempDir);
      const now = Date.now();
      
      let totalFiles = 0;
      let totalSize = 0;
      let expiredFiles = 0;
      let expiredSize = 0;

      files.forEach(filename => {
        const filePath = path.join(tempDir, filename);
        
        try {
          const stats = fs.statSync(filePath);
          
          if (stats.isFile()) {
            totalFiles++;
            totalSize += stats.size;
            
            const fileAge = now - stats.mtime.getTime();
            if (fileAge > this.FILE_MAX_AGE) {
              expiredFiles++;
              expiredSize += stats.size;
            }
          }
        } catch (error) {
          this.ctx.logger.error('获取文件状态失败', error, { filename });
        }
      });

      return { totalFiles, totalSize, expiredFiles, expiredSize };
    } catch (error) {
      this.ctx.logger.error('获取临时目录状态失败', error);
      return { totalFiles: 0, totalSize: 0, expiredFiles: 0, expiredSize: 0 };
    }
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
