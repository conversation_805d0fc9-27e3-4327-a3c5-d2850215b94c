import { Controller, Post, Get, Inject, Body } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { FileCleanupService } from '../service/file-cleanup.service';
import { Rule, RuleType } from '@midwayjs/validate';

class DeleteFileDTO {
  @Rule(RuleType.string().required().messages({
    'any.required': '文件名不能为空',
    'string.empty': '文件名不能为空',
  }))
  filename: string;
}

@Controller('/api/file-management')
export class FileManagementController {
  @Inject()
  ctx: Context;

  @Inject()
  fileCleanupService: FileCleanupService;

  /**
   * 获取临时文件目录状态
   */
  @Get('/temp-status')
  async getTempStatus() {
    try {
      const status = this.fileCleanupService.getTempDirStatus();
      
      return {
        errCode: 0,
        msg: '获取成功',
        data: {
          totalFiles: status.totalFiles,
          totalSize: this.formatFileSize(status.totalSize),
          totalSizeBytes: status.totalSize,
          expiredFiles: status.expiredFiles,
          expiredSize: this.formatFileSize(status.expiredSize),
          expiredSizeBytes: status.expiredSize,
          cleanupInfo: {
            interval: '每2小时',
            maxAge: '2小时',
            nextCleanup: '下次定时清理时间取决于服务启动时间'
          }
        }
      };
    } catch (error) {
      this.ctx.logger.error('获取临时文件状态失败', error);
      return {
        errCode: 500,
        msg: '获取临时文件状态失败',
        data: null
      };
    }
  }

  /**
   * 手动执行文件清理
   */
  @Post('/cleanup')
  async manualCleanup() {
    try {
      // 获取清理前的状态
      const beforeStatus = this.fileCleanupService.getTempDirStatus();
      
      // 执行清理
      this.fileCleanupService.cleanupExpiredFiles();
      
      // 获取清理后的状态
      const afterStatus = this.fileCleanupService.getTempDirStatus();
      
      const deletedFiles = beforeStatus.expiredFiles;
      const deletedSize = beforeStatus.expiredSize;
      
      return {
        errCode: 0,
        msg: '清理完成',
        data: {
          deletedFiles,
          deletedSize: this.formatFileSize(deletedSize),
          deletedSizeBytes: deletedSize,
          before: {
            totalFiles: beforeStatus.totalFiles,
            totalSize: this.formatFileSize(beforeStatus.totalSize),
            expiredFiles: beforeStatus.expiredFiles,
            expiredSize: this.formatFileSize(beforeStatus.expiredSize)
          },
          after: {
            totalFiles: afterStatus.totalFiles,
            totalSize: this.formatFileSize(afterStatus.totalSize),
            expiredFiles: afterStatus.expiredFiles,
            expiredSize: this.formatFileSize(afterStatus.expiredSize)
          }
        }
      };
    } catch (error) {
      this.ctx.logger.error('手动清理文件失败', error);
      return {
        errCode: 500,
        msg: '清理文件失败',
        data: null
      };
    }
  }

  /**
   * 删除指定文件
   */
  @Post('/delete-file')
  async deleteFile(@Body() deleteFileDto: DeleteFileDTO) {
    try {
      const success = this.fileCleanupService.deleteFile(deleteFileDto.filename);
      
      if (success) {
        return {
          errCode: 0,
          msg: '文件删除成功',
          data: {
            filename: deleteFileDto.filename,
            deleted: true
          }
        };
      } else {
        return {
          errCode: 404,
          msg: '文件不存在或删除失败',
          data: {
            filename: deleteFileDto.filename,
            deleted: false
          }
        };
      }
    } catch (error) {
      this.ctx.logger.error('删除文件失败', error, { filename: deleteFileDto.filename });
      return {
        errCode: 500,
        msg: '删除文件失败',
        data: null
      };
    }
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
