<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>导出功能测试页面</h1>
    
    <div class="test-section">
        <h3>学校统计导出</h3>
        <button onclick="testSchoolStatistics()">测试学校统计导出</button>
        <div id="school-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>教师排名导出</h3>
        <button onclick="testTeacherRanking()">测试教师排名导出</button>
        <div id="ranking-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>教师统计导出</h3>
        <button onclick="testTeacherStatistics()">测试教师统计导出</button>
        <div id="teacher-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>未完成学生导出</h3>
        <button onclick="testIncompleteStudents()">测试未完成学生导出</button>
        <div id="incomplete-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3141';

        async function exportData(url, requestData, resultElementId) {
            const resultElement = document.getElementById(resultElementId);
            resultElement.innerHTML = '正在导出...';
            resultElement.className = 'result';

            try {
                const response = await fetch(API_BASE + url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.errCode === 0) {
                        // 直接使用返回的下载链接
                        const a = document.createElement('a');
                        a.href = API_BASE + result.data.downloadUrl;
                        a.download = result.data.filename;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        
                        resultElement.innerHTML = `
                            <strong>导出成功！</strong><br>
                            文件名: ${result.data.filename}<br>
                            文件ID: ${result.data.fileId}<br>
                            下载链接: <a href="${API_BASE + result.data.downloadUrl}" target="_blank">点击下载</a>
                        `;
                        resultElement.className = 'result success';
                        
                        console.log('导出成功，文件ID:', result.data.fileId);
                        return result.data;
                    } else {
                        throw new Error(result.msg || '导出失败');
                    }
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.msg || '导出失败');
                }
            } catch (error) {
                console.error('导出失败:', error);
                resultElement.innerHTML = `<strong>导出失败:</strong> ${error.message}`;
                resultElement.className = 'result error';
                throw error;
            }
        }

        async function testSchoolStatistics() {
            const requestData = {
                export_type: 'school_statistics',
                sso_school_code: 'SCHOOL001',
                questionnaire_id: 1,
                month: '2024-03',
                include_trend: true,
                include_teacher_ranking: true
            };

            await exportData('/api/export/school-statistics', requestData, 'school-result');
        }

        async function testTeacherRanking() {
            const requestData = {
                export_type: 'teacher_ranking',
                sso_school_code: 'SCHOOL001',
                month: '2024-03',
                sort_by: 'average_score', // 可选: average_score, evaluation_count
                sort_order: 'DESC',
                limit: 50
            };

            await exportData('/api/export/teacher-ranking', requestData, 'ranking-result');
        }

        async function testTeacherStatistics() {
            const requestData = {
                export_type: 'teacher_statistics',
                sso_school_code: 'SCHOOL001',
                sso_teacher_id: 'TEACHER001',
                month: '2024-03',
                include_distribution: true,
                include_keywords: true,
                include_trend: true
            };

            await exportData('/api/export/teacher-statistics', requestData, 'teacher-result');
        }

        async function testIncompleteStudents() {
            const requestData = {
                export_type: 'incomplete_students',
                sso_school_code: 'SCHOOL001',
                questionnaire_id: 1,
                month: '2024-03',
                grade_code: '7',
                include_contact_info: true // 目前已注释掉联系信息列
            };

            await exportData('/api/export/incomplete-students', requestData, 'incomplete-result');
        }
    </script>
</body>
</html>
