# 导出功能前端API文档

## 快速开始

所有导出接口都是POST请求，成功时返回包含下载链接的JSON格式，失败时返回JSON错误信息。

## 接口列表

| 功能 | URL | 主要参数 |
|------|-----|----------|
| 学校统计 | `/api/export/school-statistics` | `export_type`, `sso_school_code`, `questionnaire_id`(可选) |
| 教师统计 | `/api/export/teacher-statistics` | `export_type`, `sso_school_code`, `sso_teacher_id` |
| 教师排名 | `/api/export/teacher-ranking` | `export_type`, `sso_school_code`, `sort_by`, `limit` |
| 问卷响应 | `/api/export/questionnaire-responses` | `export_type`, `sso_school_code`, `questionnaire_id`(可选) |
| 综合报表 | `/api/export/comprehensive-report` | `export_type`, `sso_school_code`, 各种`include_*`选项 |
| 未完成学生 | `/api/export/incomplete-students` | `export_type`, `sso_school_code`, `questionnaire_id`(可选) |

## 通用参数

### 必填参数
- `export_type`: 导出类型（所有接口必填）
- `sso_school_code`: 学校编码（所有接口必填）

### 可选参数
- `month`: 统计月份，格式"YYYY-MM"
- `start_month`: 开始月份，格式"YYYY-MM"
- `end_month`: 结束月份，格式"YYYY-MM"
- `export_format`: 导出格式，固定为"xlsx"（可省略）

## 前端调用示例

### 基础调用模板

```javascript
async function exportData(url, requestData) {
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (response.ok) {
      const result = await response.json();

      if (result.errCode === 0) {
        // 直接使用返回的下载链接
        const a = document.createElement('a');
        a.href = result.data.downloadUrl;
        a.download = result.data.filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        console.log('导出成功，文件ID:', result.data.fileId);
        return result.data;
      } else {
        throw new Error(result.msg || '导出失败');
      }
    } else {
      const errorData = await response.json();
      throw new Error(errorData.msg || '导出失败');
    }
  } catch (error) {
    console.error('导出失败:', error);
    alert('导出失败: ' + error.message);
    throw error;
  }
}
```

### 具体使用示例

```javascript
// 1. 导出学校统计（推荐使用缓存数据）
function exportSchoolStats() {
  exportData('/api/export/school-statistics', {
    export_type: 'school_statistics',
    sso_school_code: 'SCHOOL001',
    questionnaire_id: 123, // 提供此参数使用缓存数据，速度更快
    month: '2024-03',
    include_trend: true,
    include_teacher_ranking: true
  });
}

// 2. 导出教师排名
function exportTeacherRanking() {
  exportData('/api/export/teacher-ranking', {
    export_type: 'teacher_ranking',
    sso_school_code: 'SCHOOL001',
    month: '2024-03',
    sort_by: 'average_score', // 可选: average_score, evaluation_count
    sort_order: 'DESC',       // 可选: ASC, DESC
    limit: 50                 // 导出数量，最大1000
  });
}

// 3. 导出教师个人统计
function exportTeacherStats(teacherId) {
  exportData('/api/export/teacher-statistics', {
    export_type: 'teacher_statistics',
    sso_school_code: 'SCHOOL001',
    sso_teacher_id: teacherId,
    month: '2024-03',
    include_distribution: true,
    include_keywords: true,
    include_trend: true
  });
}

// 4. 导出综合报表
function exportComprehensiveReport() {
  exportData('/api/export/comprehensive-report', {
    export_type: 'comprehensive_report',
    sso_school_code: 'SCHOOL001',
    month: '2024-03',
    include_school_summary: true,
    include_teacher_ranking: true,
    include_trend_analysis: true,
    include_completion_analysis: true
  });
}

// 5. 导出问卷响应数据
function exportQuestionnaireResponses() {
  exportData('/api/export/questionnaire-responses', {
    export_type: 'questionnaire_responses',
    sso_school_code: 'SCHOOL001',
    questionnaire_id: 1,
    month: '2024-03',
    grade_code: '7',    // 可选筛选条件
    class_code: '1',    // 可选筛选条件
    include_answers: true
  });
}

// 6. 导出未完成学生名单
function exportIncompleteStudents() {
  exportData('/api/export/incomplete-students', {
    export_type: 'incomplete_students',
    sso_school_code: 'SCHOOL001',
    questionnaire_id: 1,
    month: '2024-03',
    grade_code: '7',           // 可选筛选条件
    include_contact_info: true
  });
}
```

## 错误处理

### 常见错误码
- `400`: 参数错误
- `500`: 服务器内部错误

### 错误响应格式
```json
{
  "errCode": 500,
  "msg": "具体错误信息",
  "data": null
}
```

## 性能优化建议

### 1. 使用缓存数据
对于学校统计导出，建议提供 `questionnaire_id` 参数以使用缓存数据：
```javascript
{
  export_type: 'school_statistics',
  sso_school_code: 'SCHOOL001',
  questionnaire_id: 123,  // 关键：使用缓存数据，速度更快
  month: '2024-03'
}
```

### 2. 设置合理超时
```javascript
const controller = new AbortController();
setTimeout(() => controller.abort(), 30000); // 30秒超时

fetch(url, {
  method: 'POST',
  signal: controller.signal,
  // ... 其他配置
});
```

### 3. 显示加载状态
```javascript
// 显示加载提示
const loadingEl = document.getElementById('loading');
loadingEl.style.display = 'block';

try {
  await exportData(url, data);
} finally {
  // 隐藏加载提示
  loadingEl.style.display = 'none';
}
```

## 注意事项

1. **文件格式**: 仅支持XLSX格式，无需指定export_format参数
2. **文件名处理**: 响应头中的文件名已URL编码，需要正确解码
3. **大数据量**: 建议使用缓存数据导出，避免超时
4. **并发限制**: 避免同时发起多个导出请求
5. **错误提示**: 必须处理导出失败情况，给用户友好提示
6. **浏览器兼容**: 支持所有现代浏览器

## 调试技巧

1. **查看网络请求**: 使用浏览器开发者工具查看请求和响应
2. **检查响应头**: 确认Content-Type和Content-Disposition
3. **验证参数**: 确保必填参数正确，可选参数格式正确
4. **服务器日志**: 查看服务器日志了解详细错误信息
