import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 导出类型枚举
 */
export enum ExportType {
  SCHOOL_STATISTICS = 'school_statistics',
  TEACHER_STATISTICS = 'teacher_statistics',
  TEACHER_RANKING = 'teacher_ranking',
  QUESTIONNAIRE_RESPONSES = 'questionnaire_responses',
  COMPREHENSIVE_REPORT = 'comprehensive_report',
  INCOMPLETE_STUDENTS = 'incomplete_students',
}

/**
 * 导出格式枚举
 */
export enum ExportFormat {
  XLSX = 'xlsx',
  CSV = 'csv',
}

/**
 * 基础导出DTO
 */
export class BaseExportDTO {
  @Rule(
    RuleType.string()
      .valid(...Object.values(ExportType))
      .required()
      .messages({
        'any.required': '导出类型不能为空',
        'any.only': '导出类型必须是有效值',
      })
  )
  export_type: ExportType;

  @Rule(
    RuleType.string()
      .valid(...Object.values(ExportFormat))
      .default(ExportFormat.XLSX)
      .messages({
        'any.only': '导出格式必须是有效值',
      })
  )
  export_format?: ExportFormat;

  @Rule(
    RuleType.string().max(50).required().messages({
      'string.max': '学校编码不能超过50个字符',
      'any.required': '学校编码不能为空',
    })
  )
  sso_school_code: string;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.pattern.base': '月份格式必须为YYYY-MM',
      })
  )
  month?: string;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.pattern.base': '开始月份格式必须为YYYY-MM',
      })
  )
  start_month?: string;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.pattern.base': '结束月份格式必须为YYYY-MM',
      })
  )
  end_month?: string;
}

/**
 * 学校统计导出DTO
 */
export class SchoolStatisticsExportDTO extends BaseExportDTO {
  @Rule(
    RuleType.number().integer().min(1).optional().messages({
      'number.min': '问卷ID必须大于0',
      'number.integer': '问卷ID必须是整数',
    })
  )
  questionnaire_id?: number;

  @Rule(RuleType.boolean().optional())
  include_trend?: boolean;

  @Rule(RuleType.boolean().optional())
  include_teacher_ranking?: boolean;

  @Rule(RuleType.boolean().optional())
  include_incomplete_students?: boolean;
}

/**
 * 教师统计导出DTO
 */
export class TeacherStatisticsExportDTO extends BaseExportDTO {
  @Rule(
    RuleType.string().max(50).required().messages({
      'string.max': '教师ID不能超过50个字符',
      'any.required': '教师ID不能为空',
    })
  )
  sso_teacher_id: string;

  @Rule(RuleType.boolean().optional())
  include_distribution?: boolean;

  @Rule(RuleType.boolean().optional())
  include_keywords?: boolean;

  @Rule(RuleType.boolean().optional())
  include_trend?: boolean;
}

/**
 * 教师排名导出DTO
 */
export class TeacherRankingExportDTO extends BaseExportDTO {
  @Rule(
    RuleType.string().max(50).optional().messages({
      'string.max': '科目不能超过50个字符',
    })
  )
  subject?: string;

  @Rule(
    RuleType.string().max(50).optional().messages({
      'string.max': '部门不能超过50个字符',
    })
  )
  department?: string;

  @Rule(
    RuleType.string()
      .valid('average_score', 'evaluation_count')
      .default('average_score')
      .messages({
        'any.only':
          '排序字段只能是average_score或evaluation_count',
      })
  )
  sort_by?: string;

  @Rule(
    RuleType.string().valid('ASC', 'DESC').default('DESC').messages({
      'any.only': '排序方向只能是ASC或DESC',
    })
  )
  sort_order?: string;

  @Rule(
    RuleType.number().integer().min(1).max(1000).default(100).messages({
      'number.min': '导出数量不能小于1',
      'number.max': '导出数量不能超过1000',
      'number.integer': '导出数量必须是整数',
    })
  )
  limit?: number;
}

/**
 * 问卷响应导出DTO
 */
export class QuestionnaireResponsesExportDTO extends BaseExportDTO {
  @Rule(
    RuleType.number().integer().min(1).optional().messages({
      'number.min': '问卷ID必须大于0',
      'number.integer': '问卷ID必须是整数',
    })
  )
  questionnaire_id?: number;

  @Rule(
    RuleType.string().max(20).optional().messages({
      'string.max': '家长手机号不能超过20个字符',
    })
  )
  parent_phone?: string;

  @Rule(
    RuleType.string().max(50).optional().messages({
      'string.max': '学生编码不能超过50个字符',
    })
  )
  sso_student_code?: string;

  @Rule(
    RuleType.string().max(10).optional().messages({
      'string.max': '年级编码不能超过10个字符',
    })
  )
  grade_code?: string;

  @Rule(
    RuleType.string().max(10).optional().messages({
      'string.max': '班级编码不能超过10个字符',
    })
  )
  class_code?: string;

  @Rule(RuleType.boolean().optional())
  is_completed?: boolean;

  @Rule(RuleType.boolean().optional())
  include_answers?: boolean;
}

/**
 * 综合报表导出DTO
 */
export class ComprehensiveReportExportDTO extends BaseExportDTO {
  @Rule(RuleType.boolean().optional())
  include_school_summary?: boolean;

  @Rule(RuleType.boolean().optional())
  include_teacher_ranking?: boolean;

  @Rule(RuleType.boolean().optional())
  include_grade_analysis?: boolean;

  @Rule(RuleType.boolean().optional())
  include_subject_analysis?: boolean;

  @Rule(RuleType.boolean().optional())
  include_trend_analysis?: boolean;

  @Rule(RuleType.boolean().optional())
  include_completion_analysis?: boolean;
}

/**
 * 未完成学生导出DTO
 */
export class IncompleteStudentsExportDTO extends BaseExportDTO {
  @Rule(
    RuleType.number().integer().min(1).optional().messages({
      'number.min': '问卷ID必须大于0',
      'number.integer': '问卷ID必须是整数',
    })
  )
  questionnaire_id?: number;

  @Rule(
    RuleType.string().max(10).optional().messages({
      'string.max': '年级编码不能超过10个字符',
    })
  )
  grade_code?: string;

  @Rule(
    RuleType.string().max(10).optional().messages({
      'string.max': '班级编码不能超过10个字符',
    })
  )
  class_code?: string;

  @Rule(RuleType.boolean().optional())
  include_contact_info?: boolean;
}
