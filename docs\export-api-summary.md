# 导出功能API总结文档

## 📋 接口概览

| 接口名称 | URL | 方法 | 主要用途 |
|----------|-----|------|----------|
| 学校统计导出 | `/api/export/school-statistics` | POST | 导出学校整体统计数据 |
| 教师统计导出 | `/api/export/teacher-statistics` | POST | 导出单个教师统计数据 |
| 教师排名导出 | `/api/export/teacher-ranking` | POST | 导出教师排名列表 |
| 问卷响应导出 | `/api/export/questionnaire-responses` | POST | 导出问卷响应明细 |
| 综合报表导出 | `/api/export/comprehensive-report` | POST | 导出综合分析报表 |
| 未完成学生导出 | `/api/export/incomplete-students` | POST | 导出未完成问卷学生名单 |
| 通用导出接口 | `/api/export/general` | POST | 通用导出入口 |

## 🚀 快速使用

### 1. 学校统计导出（推荐）
```javascript
// 使用缓存数据，速度快
const response = await fetch('/api/export/school-statistics', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    export_type: 'school_statistics',
    sso_school_code: 'SCHOOL001',
    questionnaire_id: 123,  // 关键：使用缓存数据
    month: '2024-03',
    include_trend: true,
    include_teacher_ranking: true
  })
});
```

### 2. 教师排名导出
```javascript
const response = await fetch('/api/export/teacher-ranking', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    export_type: 'teacher_ranking',
    sso_school_code: 'SCHOOL001',
    month: '2024-03',
    sort_by: 'average_score',
    sort_order: 'DESC',
    limit: 50
  })
});
```

### 3. 教师个人统计导出
```javascript
const response = await fetch('/api/export/teacher-statistics', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    export_type: 'teacher_statistics',
    sso_school_code: 'SCHOOL001',
    sso_teacher_id: 'TEACHER001',
    month: '2024-03',
    include_distribution: true,
    include_keywords: true
  })
});
```

## 📥 文件下载处理

```javascript
async function downloadExport(url, requestData) {
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData)
    });

    if (response.ok) {
      const blob = await response.blob();
      
      // 获取文件名
      const contentDisposition = response.headers.get('Content-Disposition');
      const filename = contentDisposition 
        ? decodeURIComponent(contentDisposition.split('filename=')[1].replace(/"/g, ''))
        : 'export.xlsx';
      
      // 创建下载
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } else {
      const errorData = await response.json();
      throw new Error(errorData.msg || '导出失败');
    }
  } catch (error) {
    console.error('导出失败:', error);
    alert('导出失败: ' + error.message);
  }
}
```

## 📊 参数说明

### 通用必填参数
- `export_type`: 导出类型（所有接口必填）
- `sso_school_code`: 学校编码（所有接口必填）

### 通用可选参数
- `month`: 统计月份，格式"YYYY-MM"
- `start_month`: 开始月份，格式"YYYY-MM"
- `end_month`: 结束月份，格式"YYYY-MM"
- `export_format`: 导出格式，目前仅支持"xlsx"

### 特殊参数
- `questionnaire_id`: 问卷ID（学校统计导出时提供可使用缓存数据）
- `sso_teacher_id`: 教师ID（教师统计导出必填）
- `sort_by`: 排序字段（教师排名导出）
  - `average_score`: 平均分（默认）
  - `evaluation_count`: 评价数量
  - `recommendation_rate`: 推荐率
- `sort_order`: 排序方向（`ASC`/`DESC`，默认`DESC`）
- `limit`: 导出数量（1-1000，默认100）

### 包含选项（boolean类型）
- `include_trend`: 包含趋势分析
- `include_teacher_ranking`: 包含教师排名
- `include_distribution`: 包含评分分布
- `include_keywords`: 包含关键词分析
- `include_answers`: 包含答案详情
- `include_contact_info`: 包含联系信息
- `include_school_summary`: 包含学校概览
- `include_grade_analysis`: 包含年级分析
- `include_subject_analysis`: 包含科目分析
- `include_completion_analysis`: 包含完成情况分析

## ⚡ 性能优化

### 1. 优先使用缓存数据
```javascript
// ✅ 推荐：使用缓存数据
{
  export_type: 'school_statistics',
  sso_school_code: 'SCHOOL001',
  questionnaire_id: 123,  // 提供此参数
  month: '2024-03'
}

// ⚠️ 较慢：实时计算
{
  export_type: 'school_statistics',
  sso_school_code: 'SCHOOL001',
  month: '2024-03'  // 未提供questionnaire_id
}
```

### 2. 设置合理超时时间
```javascript
const controller = new AbortController();
setTimeout(() => controller.abort(), 30000); // 30秒

fetch(url, {
  method: 'POST',
  signal: controller.signal,
  // ...
});
```

### 3. 避免并发导出
```javascript
let isExporting = false;

async function safeExport(url, data) {
  if (isExporting) {
    alert('正在导出中，请稍候...');
    return;
  }
  
  isExporting = true;
  try {
    await downloadExport(url, data);
  } finally {
    isExporting = false;
  }
}
```

## ❌ 错误处理

### 错误响应格式
```json
{
  "errCode": 500,
  "msg": "具体错误信息",
  "data": null
}
```

### 常见错误
- `400`: 参数错误（检查必填参数和格式）
- `500`: 服务器错误（查看错误信息）

### 错误处理示例
```javascript
try {
  const response = await fetch(url, options);
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.msg || `HTTP ${response.status}`);
  }
  
  // 处理成功响应...
} catch (error) {
  console.error('导出失败:', error);
  
  // 用户友好的错误提示
  if (error.message.includes('timeout')) {
    alert('导出超时，请稍后重试或联系管理员');
  } else if (error.message.includes('参数')) {
    alert('参数错误，请检查输入');
  } else {
    alert('导出失败: ' + error.message);
  }
}
```

## 🔧 调试技巧

1. **检查网络请求**: 使用浏览器开发者工具查看请求详情
2. **验证参数**: 确保JSON格式正确，必填参数不为空
3. **查看响应头**: 确认Content-Type和Content-Disposition
4. **测试小数据量**: 先用小范围数据测试，再扩大范围

## 📝 最佳实践

1. **显示加载状态**: 导出过程中显示进度提示
2. **参数验证**: 前端先验证参数格式和必填项
3. **缓存优先**: 大数据量场景优先使用缓存数据
4. **错误友好**: 提供清晰的错误提示信息
5. **避免重复**: 防止用户重复点击导出按钮
