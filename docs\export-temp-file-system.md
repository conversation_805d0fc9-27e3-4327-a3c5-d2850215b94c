# 导出功能临时文件系统说明

## 🔄 系统变更

导出功能已从直接返回文件流改为临时文件系统，提升用户体验和系统性能。

## 📁 文件存储机制

### 存储位置
- **目录**: `public/temp/`
- **文件命名**: `{uuid}_{原始文件名}`
- **访问路径**: `/public/temp/{文件名}`

### 文件生命周期
1. **生成**: 导出请求时创建临时文件
2. **存储**: 保存在public/temp目录
3. **访问**: 通过HTTP直接下载
4. **清理**: 2小时后自动删除

## 🔧 技术实现

### 服务端变更
```typescript
// 生成唯一文件ID和临时文件路径
const fileId = uuidv4();
const tempFilename = `${fileId}_${filename}`;
const publicDir = path.join(process.cwd(), 'public', 'temp');
const filePath = path.join(publicDir, tempFilename);

// 写入临时文件
fs.writeFileSync(filePath, buffer as Buffer);

// 设置2小时后自动删除
setTimeout(() => {
  this.deleteTemporaryFile(filePath);
}, 2 * 60 * 60 * 1000);

// 返回下载链接
return {
  downloadUrl: `/public/temp/${tempFilename}`,
  filename,
  fileId,
};
```

### 响应格式
```json
{
  "errCode": 0,
  "msg": "导出成功",
  "data": {
    "downloadUrl": "/public/temp/uuid_filename.xlsx",
    "filename": "学校统计报表_SCHOOL001_2024-03.xlsx", 
    "fileId": "550e8400-e29b-41d4-a716-************"
  }
}
```

## 🌐 前端调用

### 新的调用方式
```javascript
async function exportData(url, requestData) {
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData)
    });

    if (response.ok) {
      const result = await response.json();
      
      if (result.errCode === 0) {
        // 直接使用返回的下载链接
        const a = document.createElement('a');
        a.href = result.data.downloadUrl;
        a.download = result.data.filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        console.log('导出成功，文件ID:', result.data.fileId);
        return result.data;
      } else {
        throw new Error(result.msg || '导出失败');
      }
    }
  } catch (error) {
    console.error('导出失败:', error);
    alert('导出失败: ' + error.message);
  }
}
```

### 使用示例
```javascript
// 导出学校统计
exportData('/api/export/school-statistics', {
  export_type: 'school_statistics',
  sso_school_code: 'SCHOOL001',
  questionnaire_id: 123,
  month: '2024-03'
});
```

## ✅ 优势对比

### 新系统优势
- ✅ **更好的用户体验**: 立即返回响应，不需要等待大文件传输
- ✅ **支持大文件**: 不受HTTP传输限制
- ✅ **可重复下载**: 2小时内可多次下载同一文件
- ✅ **减少内存占用**: 服务器不需要保持长连接
- ✅ **更好的错误处理**: 文件生成和下载分离

### 旧系统问题
- ❌ **传输超时**: 大文件可能导致请求超时
- ❌ **内存占用**: 需要在内存中保持完整文件
- ❌ **单次下载**: 无法重复下载
- ❌ **用户体验差**: 需要等待完整传输

## 🔒 安全考虑

### 文件安全
- **唯一标识**: 使用UUID防止文件名冲突和猜测
- **自动清理**: 2小时后自动删除，防止磁盘空间占用
- **访问控制**: 文件存储在public目录，但文件名不可预测

### 建议增强
- **访问日志**: 记录文件下载日志
- **权限验证**: 可考虑添加下载权限验证
- **文件加密**: 敏感数据可考虑文件加密

## 📊 监控指标

### 关键指标
- **文件生成成功率**: 导出请求成功率
- **文件下载成功率**: 用户下载成功率
- **磁盘使用情况**: temp目录空间占用
- **清理任务状态**: 自动删除任务执行情况

### 日志记录
```typescript
// 文件生成日志
this.ctx.logger.info('临时文件已生成', {
  fileId,
  filename,
  filePath,
  size: buffer.length
});

// 文件删除日志
this.ctx.logger.info('临时文件已删除', { filePath });
```

## 🔧 运维建议

### 定期维护
1. **监控磁盘空间**: 定期检查temp目录大小
2. **清理异常文件**: 清理超时未删除的文件
3. **性能监控**: 监控文件生成和下载性能

### 故障处理
1. **磁盘满**: 清理temp目录或扩容
2. **权限问题**: 确保temp目录可写权限
3. **文件损坏**: 重新生成导出文件

## 📋 总结

临时文件系统显著提升了导出功能的用户体验和系统性能，通过合理的文件管理和自动清理机制，确保了系统的稳定性和安全性。前端调用方式简化，用户可以更快速地获得导出结果。
