# 文件清理定时任务说明

## 📋 概述

为了优化临时文件管理，已将删除临时文件的逻辑从单个文件的setTimeout改为统一的定时任务处理。

## 🔄 变更内容

### 旧方案问题
- **内存占用**: 每个文件都有一个setTimeout，大量文件时占用内存
- **不可控**: 无法统一管理和监控文件清理
- **资源浪费**: 定时器分散，资源利用率低

### 新方案优势
- **统一管理**: 所有文件清理由一个定时任务处理
- **可监控**: 提供管理接口查看清理状态
- **高效**: 批量处理，减少系统开销
- **可控**: 支持手动触发清理

## 🛠️ 技术实现

### 1. 文件清理服务 (`FileCleanupService`)

```typescript
@Provide()
export class FileCleanupService {
  private readonly CLEANUP_INTERVAL = 2 * 60 * 60 * 1000; // 2小时
  private readonly FILE_MAX_AGE = 2 * 60 * 60 * 1000; // 2小时

  @Init()
  async init() {
    this.startCleanupTask();
  }

  private startCleanupTask(): void {
    // 立即执行一次清理
    this.cleanupExpiredFiles();

    // 设置定时任务，每2小时执行一次
    setInterval(() => {
      this.cleanupExpiredFiles();
    }, this.CLEANUP_INTERVAL);
  }
}
```

### 2. 清理逻辑

```typescript
public cleanupExpiredFiles(): void {
  const tempDir = path.join(process.cwd(), 'public', 'temp');
  const files = fs.readdirSync(tempDir);
  const now = Date.now();
  
  files.forEach(filename => {
    const filePath = path.join(tempDir, filename);
    const stats = fs.statSync(filePath);
    
    // 检查文件年龄
    const fileAge = now - stats.mtime.getTime();
    
    // 删除超过2小时的文件
    if (fileAge > this.FILE_MAX_AGE) {
      fs.unlinkSync(filePath);
      this.ctx.logger.info('已删除过期临时文件', {
        filename,
        fileAge: Math.round(fileAge / 1000 / 60),
        fileSize: this.formatFileSize(stats.size)
      });
    }
  });
}
```

### 3. 服务注册

```typescript
// src/configuration.ts
export class MainConfiguration {
  async onReady() {
    // 初始化文件清理服务
    const fileCleanupService = await this.app
      .getApplicationContext()
      .getAsync(FileCleanupService);
  }
}
```

## 📊 管理接口

### 1. 查看文件状态

**接口**: `GET /api/file-management/temp-status`

**响应**:
```json
{
  "errCode": 0,
  "msg": "获取成功",
  "data": {
    "totalFiles": 15,
    "totalSize": "45.2 MB",
    "totalSizeBytes": 47447040,
    "expiredFiles": 3,
    "expiredSize": "12.1 MB",
    "expiredSizeBytes": 12689408,
    "cleanupInfo": {
      "interval": "每2小时",
      "maxAge": "2小时"
    }
  }
}
```

### 2. 手动清理

**接口**: `POST /api/file-management/cleanup`

**响应**:
```json
{
  "errCode": 0,
  "msg": "清理完成",
  "data": {
    "deletedFiles": 3,
    "deletedSize": "12.1 MB",
    "deletedSizeBytes": 12689408,
    "before": {
      "totalFiles": 15,
      "totalSize": "45.2 MB",
      "expiredFiles": 3,
      "expiredSize": "12.1 MB"
    },
    "after": {
      "totalFiles": 12,
      "totalSize": "33.1 MB",
      "expiredFiles": 0,
      "expiredSize": "0 B"
    }
  }
}
```

### 3. 删除指定文件

**接口**: `POST /api/file-management/delete-file`

**请求**:
```json
{
  "filename": "550e8400-e29b-41d4-a716-446655440000_学校统计报表.xlsx"
}
```

**响应**:
```json
{
  "errCode": 0,
  "msg": "文件删除成功",
  "data": {
    "filename": "550e8400-e29b-41d4-a716-446655440000_学校统计报表.xlsx",
    "deleted": true
  }
}
```

## ⏰ 执行时机

### 定时执行
- **频率**: 每2小时执行一次
- **首次执行**: 服务启动时立即执行一次
- **执行时间**: 基于服务启动时间计算

### 手动执行
- **管理接口**: 通过API手动触发
- **即时生效**: 立即扫描并清理过期文件

## 📝 日志记录

### 启动日志
```
文件清理定时任务已启动，每2小时执行一次
```

### 清理日志
```
已删除过期临时文件 filename=uuid_file.xlsx fileAge=125 fileSize=2.3MB
临时文件清理完成 deletedCount=3 totalSize=7.2MB remainingFiles=12
```

### 错误日志
```
清理临时文件时发生错误 error=... tempDir=/path/to/temp
删除文件失败 error=... filename=file.xlsx filePath=/path/to/file
```

## 🔧 配置参数

### 时间配置
```typescript
private readonly CLEANUP_INTERVAL = 2 * 60 * 60 * 1000; // 清理间隔：2小时
private readonly FILE_MAX_AGE = 2 * 60 * 60 * 1000;     // 文件最大年龄：2小时
```

### 目录配置
```typescript
const tempDir = path.join(process.cwd(), 'public', 'temp'); // 临时文件目录
```

## 🚀 部署注意事项

### 1. 服务启动
- 确保FileCleanupService在应用启动时正确初始化
- 检查启动日志确认定时任务已启动

### 2. 权限检查
- 确保应用有temp目录的读写权限
- 检查文件删除权限

### 3. 监控建议
- 定期检查temp目录大小
- 监控清理任务执行日志
- 设置磁盘空间告警

### 4. 故障处理
- 如果定时任务失败，可通过管理接口手动清理
- 检查文件系统权限和磁盘空间
- 查看应用日志排查问题

## 📋 总结

新的定时任务机制提供了更好的文件管理能力：

1. **统一管理**: 所有临时文件由统一的定时任务清理
2. **可监控**: 提供完整的管理接口和日志记录
3. **高效**: 批量处理，减少系统资源占用
4. **可控**: 支持手动清理和状态查询
5. **稳定**: 避免了大量setTimeout带来的内存问题

这种设计更适合生产环境的长期稳定运行。
